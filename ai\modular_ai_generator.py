#!/usr/bin/env python3
"""
Modular AI Configuration Generator

This system uses a hybrid approach:
1. AI generates complete map and structure strategy (NEW!)
2. Generate procedural framework using AI strategy
3. Use focused AI calls for specific components (terrain, enemy strategy, themes)
4. Integrate AI pieces into procedural structure

This is more reliable than full AI JSON generation while using AI creativity throughout.
"""

import json
import os
import math
from typing import Dict, Any, Optional, Callable, List, Tuple
from openai import AzureOpenAI
from datetime import datetime

# Import comprehensive difficulty knowledge
from .difficulty_factors import (
    get_difficulty_70_reference, 
    understand_difficulty_components, 
    calculate_target_difficulty_adjustment,
    get_enemy_type_difficulty,
    get_tower_effectiveness_matrix
)

# Import buff system performance analysis
from .performance_analysis import (
    analyze_buff_performance_trends,
    get_buff_difficulty_recommendations,
    generate_buff_config_from_performance
)


class TowerType:
    """Tower specification for analytical balancing"""
    def __init__(self, name: str, cost: int, build_time: float, dps_curve: Callable[[float], float]):
        self.name = name
        self.cost = cost
        self.build_time = build_time  # seconds until it fires
        self.dps_curve = dps_curve    # function of t_since_built


class WaveEnemy:
    """Enemy specification for analytical balancing"""
    def __init__(self, hp: float):
        self.hp = hp


class ModularAIGenerator:
    """Modular AI generator that uses AI for complete map design and strategic components"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.ai_available = False
        self.client = None
        
        # Initialize AI if available
        if api_key:
            try:
                self.client = AzureOpenAI(
                    api_key=api_key,
                    api_version="2025-01-01-preview",
                    azure_endpoint="https://matrixnewopenai.openai.azure.com"
                )
                self.deployment_name = "gpt-4o"
                self.ai_available = True
                print("🧩🤖 MODULAR AI INITIALIZED - Using AI for complete map design!")
            except Exception as e:
                print(f"🧩❌ Modular AI initialization failed: {e}")
                self.ai_available = False
        
        # Load base procedural generator
        try:
            from .make_specific_config import ConfigGenerator
            self.base_generator = ConfigGenerator()
        except ImportError:
            import sys
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from make_specific_config import ConfigGenerator
            self.base_generator = ConfigGenerator()
    
    def generate_modular_config(self, performance_data: Dict[str, Any], 
                               width: int = 20, height: int = 15, 
                               total_waves: int = 80) -> Dict[str, Any]:
        """Generate config using fully AI-driven modular approach"""
        
        print("🧩🎨 COMPREHENSIVE AI MAP GENERATION STARTING...")
        print("   Step 1: AI designs complete map structure and layout")
        print("   Step 2: Apply AI map strategy to procedural generation")
        print("   Step 3: AI designs economic adjustments (money, lives, rewards)")
        print("   Step 4: AI designs wave progression system") 
        print("   Step 5: AI designs terrain strategy")
        print("   Step 6: AI designs enemy counter-strategy")
        print("   Step 7: AI creates level theme and naming")
        print("   Step 8: Integrate all AI components")
        
        # ★ ENHANCED: Use tower_defense_game.json as difficulty 70 reference ★
        performance_score = performance_data.get('score', 50)

        # Check if previous config was the reference tower_defense_game.json
        config_file_path = performance_data.get('config_file_path', '')
        is_reference_config = 'tower_defense_game.json' in config_file_path

        # Get previous difficulty with proper reference understanding
        previous_difficulty = 50  # Default fallback

        if is_reference_config:
            # This was the reference config - it represents difficulty 70
            previous_difficulty = 70
            print(f"   📊 🎯 REFERENCE CONFIG DETECTED: tower_defense_game.json = Difficulty 70")
        else:
            # Try to get difficulty from config details
            previous_config_details = performance_data.get('previous_config_details')
            if previous_config_details:
                previous_difficulty = previous_config_details.get('difficulty', 50)
                print(f"   📊 Using previous difficulty from config: {previous_difficulty}")
            else:
                # Check if we have the config difficulty score from the enhanced performance data
                config_difficulty_score = performance_data.get('config_difficulty_score')
                if config_difficulty_score:
                    previous_difficulty = config_difficulty_score
                    print(f"   📊 Using config difficulty score: {previous_difficulty}")
                else:
                    # Final fallback to old method
                    previous_difficulty = performance_data.get('previous_difficulty', 50)
                    print(f"   📊 Using fallback previous difficulty: {previous_difficulty}")
        
        # Calculate NEW difficulty based on performance with reference understanding
        # ENHANCED: Use difficulty 70 reference for better scaling
        win_flag = performance_data.get('win_flag', False)

        # Get difficulty 70 reference for context
        difficulty_70_reference = get_difficulty_70_reference()

        if win_flag:
            # Player WON - can increase difficulty based on performance
            if performance_score >= 80:
                difficulty_change = 15  # Max increase for excellent performance
            elif performance_score >= 60:
                difficulty_change = 8   # Moderate increase for good performance
            elif performance_score >= 40:
                difficulty_change = 5   # Small increase for average performance
            else:
                difficulty_change = 2   # Minimal increase for poor but winning performance
        else:
            # Player LOST - always reduce difficulty to help them succeed
            if performance_score >= 70:
                difficulty_change = -5  # Small decrease for high-scoring losses
            elif performance_score >= 50:
                difficulty_change = -10 # Moderate decrease for medium-scoring losses
            elif performance_score >= 30:
                difficulty_change = -15 # Large decrease for low-scoring losses
            else:
                difficulty_change = -20 # Major decrease for very poor performance

        # Apply gradual difficulty change (never jump more than 15 levels)
        base_difficulty = max(1, min(100, previous_difficulty + difficulty_change))
        base_difficulty = int(round(base_difficulty))

        # Add reference context to the output
        reference_context = ""
        if is_reference_config:
            reference_context = " (from reference config)"

        print(f"   🎯 Difficulty progression: {previous_difficulty}{reference_context} → {base_difficulty} (change: {difficulty_change:+d})")
        print(f"   📚 Using Difficulty 70 Reference: {difficulty_70_reference['description']}")
        
        # Step 1: AI Map Structure Design
        map_strategy = None
        if self.ai_available:
            # Add reference config context to performance data for AI methods
            enhanced_performance_data = performance_data.copy()
            enhanced_performance_data['is_reference_config'] = is_reference_config
            enhanced_performance_data['reference_difficulty'] = 70 if is_reference_config else previous_difficulty
            enhanced_performance_data['difficulty_70_reference'] = difficulty_70_reference

            map_strategy = self._generate_ai_map_structure(enhanced_performance_data, width, height)
            if map_strategy:
                print("🧩🗺️ AI map structure strategy integrated")
            else:
                print("🧩⚠️ AI map structure failed, using standard approach")
        
        # Step 2: Apply AI map strategy to procedural generation
        print(f"🔧 Generating framework with AI strategy: Base difficulty {base_difficulty}")
        
        # ★ ALWAYS store previous level data for ALL modes (AI and fallback) ★
        previous_config_details = performance_data.get('previous_config_details', {})
        stored_previous_data = {}
        if previous_config_details:
            previous_wave_systems = previous_config_details.get('wave_systems', {})
            previous_progression = previous_config_details.get('progression_systems', {})
            stored_previous_data = {
                'total_waves': previous_wave_systems.get('total_waves', 80),
                'wave_progression': previous_wave_systems.get('wave_progression', {}),
                'boss_waves': previous_wave_systems.get('boss_waves', {}),
                'spawn_config': previous_wave_systems.get('spawn_config', {}),
                'starting_money': previous_progression.get('starting_money', 500),
                'starting_lives': previous_progression.get('starting_lives', 20),
                'enemy_rewards': previous_progression.get('enemy_rewards', {}),
                'wave_bonuses': previous_progression.get('wave_bonuses', {}),
                'previous_difficulty': previous_config_details.get('difficulty', 50)
            }
            print(f"   📊 Previous level data: {stored_previous_data['total_waves']} waves, ${stored_previous_data['starting_money']} money, difficulty {stored_previous_data['previous_difficulty']}")
        
        # Modify base generator behavior based on AI strategy
        if map_strategy:
            base_config = self._generate_config_with_ai_strategy(
                base_difficulty, width, height, total_waves, map_strategy, performance_data
            )
        else:
            # Fallback to standard generation (but use previous level data!)
            actual_total_waves = stored_previous_data.get('total_waves', total_waves) if stored_previous_data else total_waves
            print(f"   🔧 Using previous wave count: {actual_total_waves} (instead of default {total_waves})")
            
            base_config = self.base_generator.generate_config(
                difficulty=base_difficulty,
                width=width,
                height=height,
                total_waves=actual_total_waves  # ★ Use previous level's wave count! ★
            )
        
        # ★ ALWAYS store previous level data in config (for both AI and fallback modes) ★
        if stored_previous_data:
            base_config['_previous_level_data'] = stored_previous_data
        
        # Initialize AI modifications tracking
        ai_adjustments = {
            'difficulty_adjustment': {
                'new_difficulty': base_difficulty,
                'change': base_difficulty - 50,
                'reasoning': f"Initial difficulty based on performance score {performance_score:.1f}%"
            },
            'map_structure_adjustments': map_strategy if map_strategy else {},
            'economic_adjustments': {},
            'wave_adjustments': {},
            'terrain_adjustments': {},
            'enemy_adjustments': {},
            'reasoning': "Comprehensive AI-driven map and component generation"
        }
        
        # Step 3: AI Economic System Design (money, lives, rewards)
        if self.ai_available:
            economic_adjustments = self._generate_ai_economic_adjustments(enhanced_performance_data)
            if economic_adjustments:
                print("🧩💰 AI economic system integrated")
                self._apply_economic_adjustments(base_config, economic_adjustments)
                ai_adjustments['economic_adjustments'] = economic_adjustments
            else:
                print("🧩⚠️ AI economic system failed, using procedural")
        else:
            # ★ NEW: Fallback economic adjustments using previous level data ★
            if stored_previous_data and base_config.get('_previous_level_data'):
                print("🔧💰 Applying fallback economic adjustments using previous level data")
                
                # Simple rule-based economic adjustments based on performance
                performance_score = performance_data.get('score', 50)
                
                # Calculate economic modifiers based on performance (reverse of AI logic)
                if performance_score >= 80:
                    money_modifier = 0.7  # Make it harder for excellent players
                    lives_modifier = 0.9
                elif performance_score >= 60:
                    money_modifier = 0.8  # Moderately harder for good players
                    lives_modifier = 0.95
                elif performance_score >= 40:
                    money_modifier = 1.0  # Same difficulty for average players
                    lives_modifier = 1.0
                else:
                    money_modifier = 0.8  # Less money for struggling players (easier challenges)
                    lives_modifier = 1.1
                
                fallback_adjustments = {
                    'starting_money_modifier': money_modifier,
                    'starting_lives_modifier': lives_modifier,
                    'wave_money_bonus_modifier': 1.0,  # Keep wave bonuses unchanged in fallback
                    'boss_money_bonus_modifier': 1.0,  # Keep boss bonuses unchanged in fallback
                    'enemy_reward_modifier': 1.0,     # Keep enemy rewards unchanged in fallback
                    'wave_money_bonus_modifier': 1.0,  # Keep wave bonuses unchanged in fallback
                    'boss_money_bonus_modifier': 1.0,  # Keep boss bonuses unchanged in fallback
                    'enemy_reward_modifier': 1.0,     # Keep enemy rewards unchanged in fallback
                    'economic_reasoning': f"Fallback adjustments for {performance_score}% performance"
                }
                
                self._apply_economic_adjustments(base_config, fallback_adjustments)
                ai_adjustments['economic_adjustments'] = fallback_adjustments
            else:
                print("🔧💰 No economic adjustments applied (no previous level data)")
        
        # Step 4: AI Wave Progression System Design
        if self.ai_available:
            wave_adjustments = self._generate_ai_wave_progression(enhanced_performance_data)
            if wave_adjustments:
                print("🧩📈 AI wave progression system integrated")
                self._apply_wave_adjustments(base_config, wave_adjustments)
                ai_adjustments['wave_adjustments'] = wave_adjustments
                # Update difficulty based on AI wave design
                if 'difficulty_modifier' in wave_adjustments:
                    new_difficulty = min(100, max(1, base_difficulty * wave_adjustments['difficulty_modifier']))
                    new_difficulty = int(round(new_difficulty))  # Round to integer for consistency
                    ai_adjustments['difficulty_adjustment']['new_difficulty'] = new_difficulty
                    ai_adjustments['difficulty_adjustment']['change'] = new_difficulty - base_difficulty
                    
                    # FIX: Update the actual config's difficulty to match
                    base_config['_generation_metadata']['difficulty'] = new_difficulty
                    base_config['_generation_metadata']['difficulty_factors']['difficulty'] = new_difficulty
            else:
                print("🧩⚠️ AI wave progression failed, using procedural")
        else:
            # ★ NEW: Even without AI, we can apply basic wave progression if needed ★
            if stored_previous_data:
                print("🔧📈 Previous level wave count already applied to base config generation")
        
        # Step 5: AI Terrain Strategy (focused, small JSON)
        if self.ai_available:
            terrain_strategy = self._generate_ai_terrain_strategy(enhanced_performance_data)
            if terrain_strategy:
                print("🧩🎨 AI terrain strategy integrated")
                self._apply_terrain_strategy(base_config, terrain_strategy)
                ai_adjustments['terrain_adjustments'] = terrain_strategy
            else:
                print("🧩⚠️ AI terrain failed, using procedural terrain")
        
        # Step 6: AI Enemy Counter-Strategy (focused, small JSON)
        if self.ai_available:
            enemy_strategy = self._generate_ai_enemy_strategy(enhanced_performance_data)
            if enemy_strategy:
                print("🧩🎯 AI enemy counter-strategy integrated")
                self._apply_enemy_strategy(base_config, enemy_strategy)
                ai_adjustments['enemy_adjustments'] = enemy_strategy
            else:
                print("🧩⚠️ AI enemy strategy failed, using procedural enemies")
        
        # Step 6.5: AI Buff System Configuration (NEW!)
        print("🧩✨ Applying AI buff system configuration...")
        buff_config = self._generate_ai_buff_system_config(enhanced_performance_data)
        if buff_config:
            print("🧩🎭 AI buff system integrated")
            base_config['enemy_buff_config'] = buff_config
            ai_adjustments['buff_system_adjustments'] = {
                'enabled': buff_config.get('enabled', True),
                'intensity': buff_config.get('buff_intensity', 'medium'),
                'reasoning': buff_config.get('ai_adjustments', {}).get('intensity_reasoning', 'AI-generated buff configuration')
            }
        else:
            print("🧩⚠️ AI buff system failed, using fallback configuration")
        
        # Step 7: AI Level Theme and Naming (focused, small JSON)
        level_name = 'AI-Designed Challenge'
        if self.ai_available:
            theme_data = self._generate_ai_theme(enhanced_performance_data, enemy_strategy if 'enemy_strategy' in locals() else None)
            if theme_data:
                level_name = theme_data.get('level_name', 'AI-Designed Challenge')
                print(f"🧩🎭 AI theme applied: {level_name}")
                base_config['level_name'] = level_name
                if 'description' in theme_data:
                    base_config['level_description'] = theme_data['description']
            else:
                print("🧩⚠️ AI theming failed, using default naming")
                base_config['level_name'] = level_name
        
        # Step 8: Add comprehensive modular metadata with required ai_adjustments field
        base_config['_generation_metadata'].update({
            'generation_method': 'modular_ai_full',
            'modular_components': {
                'map_structure': 'ai' if map_strategy else 'procedural',
                'framework': 'ai_guided_procedural',
                'economic_system': 'ai' if self.ai_available else 'procedural',
                'wave_progression': 'ai' if self.ai_available else 'procedural',
                'terrain_strategy': 'ai' if self.ai_available else 'procedural',
                'enemy_strategy': 'ai' if self.ai_available else 'procedural', 
                'theme_and_naming': 'ai' if self.ai_available else 'procedural'
            },
            'creation_type': '🧩 FULL AI MAP GENERATION',
            'reliability': 'high'
        })
        
        # Compile final reasoning
        final_difficulty = ai_adjustments['difficulty_adjustment']['new_difficulty']
        ai_adjustments['reasoning'] = f"Complete AI-driven generation: Map structure, economic system, wave progression, terrain, enemies, and theming all designed by AI for performance score {performance_score:.1f}% (Final difficulty: {final_difficulty})"
        
        # Add adaptive metadata with ai_adjustments for game compatibility
        if '_adaptive_metadata' not in base_config:
            base_config['_adaptive_metadata'] = {}
        
        # Ensure ai_adjustments is properly added to _adaptive_metadata
        base_config['_adaptive_metadata']['ai_adjustments'] = ai_adjustments
        base_config['_adaptive_metadata']['generation_timestamp'] = datetime.now().isoformat()
        base_config['_adaptive_metadata']['generation_method'] = 'modular_ai_full'
        base_config['_adaptive_metadata']['creation_type'] = '🧩 FULL AI MAP GENERATION'
        
        print("🧩✅ COMPREHENSIVE AI MAP GENERATION COMPLETE!")
        print(f"   Map Structure: {'AI-designed' if map_strategy else 'Procedural fallback'}")
        print(f"   AI Components: {'6/6 successful' if self.ai_available and map_strategy else '5/6 successful' if self.ai_available else '0/6 (fallback mode)'}")
        print(f"   Final Difficulty: {final_difficulty}")
        
        # Step 9: Analytical Balancing Integration
        print("\n🎯 STEP 9: ANALYTICAL BALANCING VALIDATION")
        balanced_config = self._apply_analytical_balancing(base_config)
        print("🎯✅ Analytical balancing complete!")
        
        return balanced_config
    
    def _generate_ai_economic_adjustments(self, performance_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate AI-designed economic system adjustments (money, lives, rewards)"""
        if not self.ai_available:
            return None
        
        score = performance_data.get('score', 50)
        win_flag = performance_data.get('win_flag', False)

        # Enhanced: Get reference config context
        is_reference_config = performance_data.get('is_reference_config', False)
        difficulty_70_reference = performance_data.get('difficulty_70_reference', {})

        # Get multi-game context if available
        multi_game_context = performance_data.get('multi_game_context', {})
        win_rate = multi_game_context.get('win_rate', 50.0) if multi_game_context else 50.0
        avg_score = multi_game_context.get('avg_score', score) if multi_game_context else score
        
        # ★ NEW: Use extracted config details from performance data ★
        previous_config_details = performance_data.get('previous_config_details', {})
        previous_difficulty = previous_config_details.get('difficulty', 50)
        previous_progression = previous_config_details.get('progression_systems', {})
        previous_economy = previous_config_details.get('economy_config', {})
        
        # Build detailed previous config summary
        previous_enemy_rewards = previous_progression.get('enemy_rewards', {})
        previous_wave_bonuses = previous_progression.get('wave_bonuses', {})
        
        # Format reward/bonus details for display
        enemy_rewards_summary = "Standard"
        if previous_enemy_rewards:
            reward_items = [f"{enemy}: ${reward}" for enemy, reward in list(previous_enemy_rewards.items())[:3]]
            if len(previous_enemy_rewards) > 3:
                reward_items.append("...")
            enemy_rewards_summary = ", ".join(reward_items) if reward_items else "Standard"
        
        wave_bonuses_summary = "Standard" 
        if previous_wave_bonuses:
            if isinstance(previous_wave_bonuses, dict):
                bonus_parts = []
                if 'normal_wave_bonus' in previous_wave_bonuses:
                    bonus_parts.append(f"Wave: ${previous_wave_bonuses['normal_wave_bonus']}")
                if 'boss_wave_bonus' in previous_wave_bonuses:
                    bonus_parts.append(f"Boss: ${previous_wave_bonuses['boss_wave_bonus']}")
                wave_bonuses_summary = ", ".join(bonus_parts) if bonus_parts else "Standard"
        
        prev_config_summary = f"""
PREVIOUS LEVEL CONFIGURATION:
• Difficulty: {previous_difficulty}/100
• Starting Money: ${previous_progression.get('starting_money', 500)}
• Starting Lives: {previous_progression.get('starting_lives', 20)}
• Enemy Rewards: {enemy_rewards_summary}
• Wave Bonuses: {wave_bonuses_summary}
• Economy Config: {previous_economy if previous_economy else 'Standard'}"""
        
        # Get comprehensive difficulty knowledge
        difficulty_70_reference = get_difficulty_70_reference()
        difficulty_components = understand_difficulty_components()
        
        # ★ ENHANCED: Add comprehensive enemy reward analysis to economic adjustments ★
        prev_config = performance_data.get('previous_config', {})
        
        # Calculate performance trend
        current_score = performance_data.get('score', 50)
        avg_score = performance_data.get('multi_game_avg', current_score)
        trend = "improving" if current_score > avg_score else "declining" if current_score < avg_score else "stable"
        
        reward_analysis = self._analyze_enemy_reward_distribution(prev_config, performance_data)
        
        prompt = f"""Design economic balance adjustments for tower defense based on comprehensive player performance analysis and enemy reward distribution.

COMPREHENSIVE GAME KNOWLEDGE - DIFFICULTY 70 REFERENCE:
This is what difficulty 70 means in our tower defense game:
{json.dumps(difficulty_70_reference, indent=2)}

DIFFICULTY COMPONENTS UNDERSTANDING:
{json.dumps(difficulty_components, indent=2)}

PLAYER PERFORMANCE ANALYSIS:
- Current Score: {score}/100
- Current Win: {win_flag}
- Multi-Game Average: {avg_score:.1f}/100
- Performance Trend: {trend}

REFERENCE CONFIG CONTEXT:
- Previous config was {'the REFERENCE tower_defense_game.json (difficulty 70)' if is_reference_config else f'a custom config (difficulty {previous_difficulty})'}
- This means the player {'has experience with the baseline difficulty 70 challenge' if is_reference_config else f'has experience with difficulty {previous_difficulty} relative to the baseline'}
- Use difficulty 70 as the foundation for understanding appropriate economic balance

PREVIOUS LEVEL ENEMY REWARD ANALYSIS:
{json.dumps(reward_analysis, indent=2)}

ECONOMIC BALANCE CONTEXT:
- Previous starting money: {previous_economy.get('starting_money', 500)}
- Previous normal wave bonus: {previous_economy.get('normal_wave_bonus', 50)}
- Previous boss wave bonus: {previous_economy.get('boss_wave_bonus', 200)}
- Previous enemy reward scaling: {previous_economy.get('reward_per_wave', 0.12)}
- Previous max reward multiplier: {previous_economy.get('max_reward_multiplier', 20.0)}

DIFFICULTY CONTEXT ANALYSIS:
- Previous difficulty was {previous_difficulty}/100
- Player achieved {score}% on that difficulty level
- Problematic enemies identified: {len(reward_analysis.get('problematic_enemies', []))}
- Reward efficiency issues: {[e['enemy'] + ' (' + e['issue'] + ')' for e in reward_analysis.get('problematic_enemies', [])]}

STRATEGIC ECONOMIC ADJUSTMENTS NEEDED:
Based on the player's performance, generate comprehensive economic adjustments that control:

1. **STARTING ECONOMY**: Adjust starting money based on performance
2. **WAVE REWARDS**: Modify normal_wave_bonus and boss_wave_bonus
3. **ENEMY REWARD SCALING**: Control reward_per_wave and max_reward_multiplier
4. **ENEMY SPAWN RATE ADJUSTMENTS**: Modify spawn rates to balance reward distribution
5. **STRATEGIC REWARD REBALANCING**: Address problematic enemy reward efficiency

SPECIFIC RULES FOR ECONOMIC ADJUSTMENTS:
- If player won easily (score > 80): REDUCE economic assistance by 10-25%
- If player won barely (score 60-80): MINOR adjustments ±5-10%
- If player lost (score < 60): INCREASE economic assistance by 10-30%
- If player lost badly (score < 40): MAJOR economic boost 20-40%

ENEMY REWARD SYSTEM CONTROL:
- Analyze previous level's enemy reward distribution
- Identify over-rewarding enemies (efficiency > 8) and DRASTICALLY reduce their spawn rates (0.3-0.7x)
- Identify under-rewarding enemies (efficiency < 2) and DRAMATICALLY increase their spawn rates (1.5-3.0x)
- Create EXTREME spawn rate differences to optimize reward balance
- For late-game waves (60-80), use MAXIMUM extreme adjustments for intense encounters

🏗️ TOWER COST PROGRESSION CONTROL:
The AI must now control DRAMATIC tower cost scaling to balance against increasing enemy difficulty and quantity:

CURRENT TOWER COST SCALING (too weak):
- Early waves (1-15): 2% cost increase per wave  
- Mid waves (16-30): 3% cost increase per wave
- Late waves (31+): 5% cost increase per wave
- Max multiplier: 3.0x (only triple cost)

REQUIRED DRAMATIC SCALING ADJUSTMENTS:
- Early increase: 3-8% per wave (vs current 2%)
- Mid increase: 5-12% per wave (vs current 3%) 
- Late increase: 8-20% per wave (vs current 5%)
- Max multiplier: 4-15x (vs current 3x)
- Player struggling: REDUCE cost scaling for economic help
- Player dominating: INCREASE cost scaling for challenge

OUTPUT REQUIREMENTS:
Generate a JSON object with these economic adjustments:

{{
  "starting_money_modifier": 0.9,  // Multiplier for starting money (0.8-1.3)
  "normal_wave_bonus_modifier": 1.1,  // Multiplier for normal wave bonus (0.7-1.4)
  "boss_wave_bonus_modifier": 1.05,  // Multiplier for boss wave bonus (0.8-1.3)
  "enemy_reward_scaling_modifier": 0.95,  // Multiplier for reward_per_wave (0.7-1.4)
  "max_reward_multiplier_modifier": 1.0,  // Multiplier for max_reward_multiplier (0.8-1.3)
  "tower_cost_progression": {{
    "early_increase_per_wave_modifier": 1.5,  // Multiplier for early cost increase (0.5-3.0)
    "mid_increase_per_wave_modifier": 2.0,    // Multiplier for mid cost increase (0.5-4.0)
    "late_increase_per_wave_modifier": 2.5,   // Multiplier for late cost increase (0.5-5.0)
    "max_cost_multiplier_modifier": 2.0      // Multiplier for max cost cap (0.7-5.0)
  }},
  "enemy_spawn_rate_adjustments": {{
    "FastEnemy": 0.4,  // DRASTICALLY reduce spawn rate (60% reduction) if over-rewarding
    "SpectralEnemy": 2.5,  // DRAMATICALLY increase spawn rate (150% increase) if under-rewarding
    "VoidEnemy": 0.3,  // EXTREME reduction for problematic enemies
    "AdaptiveEnemy": 3.0  // MAXIMUM boost for strategic late-game encounters
  }},
  "strategic_focus": "reward_balance",  // Focus area: "economy_boost", "reward_balance", "difficulty_scaling", "tower_cost_control"
  "reasoning": "Player struggled with late-game enemies. Reduced FastEnemy spawn rate (over-rewarding) and increased SpectralEnemy spawn rate (under-rewarding) to balance reward distribution. Applied dramatic tower cost scaling to match increased enemy difficulty. Slight economic boost to help player progression."
}}

STRATEGIC REASONING:
- Consider the player's multi-game performance trend
- Balance immediate economic help with long-term progression
- Address specific reward efficiency problems identified in the analysis
- Maintain challenge while ensuring fair progression
- Focus on strategic enemy spawn rate adjustments that improve reward balance"""

        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are an economic game designer. Output only JSON, no other text."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,  # Increased from 300 for comprehensive economic analysis
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = ai_response[start_idx:end_idx]
                return json.loads(json_str)
        except Exception as e:
            print(f"🧩⚠️ AI economic generation failed: {e}")
        
        return None
    
    def _generate_ai_wave_progression(self, performance_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate AI-designed wave progression system"""
        if not self.ai_available:
            return None
        
        score = performance_data.get('score', 50)
        win_flag = performance_data.get('win_flag', False)
        
        # Get multi-game context if available
        multi_game_context = performance_data.get('multi_game_context', {})
        avg_score = multi_game_context.get('avg_score', score) if multi_game_context else score
        trend = multi_game_context.get('trend', 'stable') if multi_game_context else 'stable'
        
        # ★ NEW: Use extracted config details from performance data ★
        previous_config_details = performance_data.get('previous_config_details', {})
        previous_difficulty = previous_config_details.get('difficulty', 50)
        previous_wave_systems = previous_config_details.get('wave_systems', {})
        previous_enemy_types = previous_config_details.get('enemy_types_used', [])
        
        # Build detailed wave systems summary
        wave_config_summary = f"""
PREVIOUS WAVE SYSTEMS:
• Total Waves: {previous_wave_systems.get('total_waves', 80)}
• Spawn Config: {previous_wave_systems.get('spawn_config', 'Standard')}
• Wave Progression: {previous_wave_systems.get('wave_progression', 'Standard')}
• Boss Waves: {previous_wave_systems.get('boss_waves', 'Standard')}
• Special Waves: {previous_wave_systems.get('special_waves', [])}
• Enemy Types Used: {', '.join(previous_enemy_types[:5])}{'...' if len(previous_enemy_types) > 5 else ''}"""
        
        # Get comprehensive difficulty knowledge
        difficulty_70_reference = get_difficulty_70_reference()
        difficulty_components = understand_difficulty_components()
        
        prompt = f"""Design wave progression system for tower defense based on player performance and previous wave configuration.

COMPREHENSIVE GAME KNOWLEDGE - DIFFICULTY 70 REFERENCE:
This is what difficulty 70 means in our tower defense game:
{json.dumps(difficulty_70_reference, indent=2)}

DIFFICULTY COMPONENTS UNDERSTANDING:
{json.dumps(difficulty_components, indent=2)}

PLAYER PERFORMANCE:
- Current Score: {score}/100
- Current Win: {win_flag}
- Multi-Game Average: {avg_score:.1f}/100
- Performance Trend: {trend}

{wave_config_summary}

DIFFICULTY CONTEXT ANALYSIS:
- Previous difficulty was {previous_difficulty}/100
- Player achieved {score}% on that difficulty level
- Previous wave count: {previous_wave_systems.get('total_waves', 80)} waves
- Enemy types handled: {len(previous_enemy_types)} different types
- Difficulty 70 comparison: {'Above difficulty 70 reference' if previous_difficulty > 70 else 'At difficulty 70 reference' if previous_difficulty == 70 else 'Below difficulty 70 reference'}

CRITICAL WAVE PROGRESSION RULES - WIN/LOSS PRIORITY:
- If player WON: Can increase waves based on score performance
  * 80%+ score: Moderate wave increase (1.1-1.25x)
  * 60%+ score: Small wave increase (1.05-1.15x) 
  * 40%+ score: Minimal wave increase (1.0-1.1x)
  * <40% score: Keep same wave count (1.0x)
- If player LOST: Always reduce waves to help them succeed
  * 70%+ score: Small wave reduction (0.9-0.95x)
  * 50%+ score: Moderate wave reduction (0.8-0.9x)
  * 30%+ score: Large wave reduction (0.7-0.8x)
  * <30% score: Major wave reduction (0.6-0.7x)

DIFFICULTY-BASED WAVE COUNT GUIDELINES:
- Difficulty 1-20: 60-90 waves (learning/early game)
- Difficulty 21-40: 70-95 waves (intermediate) 
- Difficulty 41-60: 80-105 waves (advanced)
- Difficulty 61-80: 90-115 waves (expert)
- Difficulty 81-100: 100-120 waves (master)

ENEMY SCALING CONTROL (per-wave growth rates):
- health_per_wave: 0.1-0.5 (how much health grows each wave)
- speed_per_wave: 0.02-0.1 (how much speed grows each wave)
- reward_per_wave: 0.05-0.25 (how much rewards grow each wave)
- size_per_wave: 0.01-0.05 (how much size grows each wave)
- damage_scaling_per_wave: 0.05-0.2 (how much damage grows each wave)

MAX MULTIPLIER CAPS (maximum growth limits):
- max_health_multiplier: 20-100 (health can grow up to Xx original)
- max_speed_multiplier: 2-8 (speed can grow up to Xx original)
- max_reward_multiplier: 10-30 (rewards can grow up to Xx original)
- max_size_multiplier: 1.5-3 (size can grow up to Xx original)
- max_damage_multiplier: 2-6 (damage can grow up to Xx original)

SPAWN RATE CONTROL:
- min_spawn_delay_modifier: 0.5-2.0 (minimum delay between spawns)
- base_spawn_delay_modifier: 0.7-1.5 (starting spawn delay)

ENEMY STAT ADJUSTMENT RULES:
- HARDER levels: Higher max health (1.1-1.5x) and speed (1.05-1.3x) for all enemies
- EASIER levels: Lower max health (0.7-0.9x) and speed (0.8-0.95x) for all enemies
- These modifiers affect BASE enemy stats, while scaling modifiers affect per-wave growth

EXAMPLE FOR THIS CASE:
Player scored {score}% and {'WON' if win_flag else 'LOST'} at difficulty {previous_difficulty}
→ {'Can increase waves moderately' if win_flag and score >= 60 else 'Can increase waves slightly' if win_flag and score >= 40 else 'Should reduce waves to help player succeed' if not win_flag else 'Keep similar wave count'}

Output ONLY this JSON format:
{{
    "total_waves_modifier": 0.5-1.5,
    "enemy_health_scaling_modifier": 0.7-1.4,
    "enemy_speed_scaling_modifier": 0.8-1.3,
    "enemy_reward_scaling_modifier": 0.8-1.3,
    "enemy_size_scaling_modifier": 0.8-1.3,
    "enemy_damage_scaling_modifier": 0.8-1.3,
    "max_health_multiplier_modifier": 0.5-2.0,
    "max_speed_multiplier_modifier": 0.6-2.0,
    "max_reward_multiplier_modifier": 0.5-2.0,
    "max_size_multiplier_modifier": 0.7-1.5,
    "max_damage_multiplier_modifier": 0.5-2.0,
    "enemy_max_health_modifier": 0.7-1.5,
    "enemy_max_speed_modifier": 0.8-1.3,
    "min_spawn_delay_modifier": 0.5-2.0,
    "base_spawn_delay_modifier": 0.7-1.5,
    "spawn_delay_reduction_modifier": 0.7-1.3,
    "boss_wave_frequency_modifier": 0.5-1.5,
    "difficulty_modifier": 0.8-1.2,
    "wave_progression_reasoning": "explanation of wave progression design choices based on WIN/LOSS status and difficulty level, including all scaling parameter adjustments"
}}"""

        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are a wave progression designer. Output only JSON, no other text."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,  # Increased from 400 for comprehensive wave progression analysis
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = ai_response[start_idx:end_idx]
                return json.loads(json_str)
        except Exception as e:
            print(f"🧩⚠️ AI wave progression generation failed: {e}")
        
        return None

    def _generate_ai_terrain_strategy(self, performance_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate focused AI terrain strategy (small, reliable JSON)"""
        if not self.ai_available:
            return None
        
        score = performance_data.get('score', 50)
        win_flag = performance_data.get('win_flag', False)
        tower_diversity = performance_data.get('tower_diversity', 3)
        
        # ★ NEW: Use extracted config details from performance data ★
        previous_config_details = performance_data.get('previous_config_details', {})
        previous_difficulty = previous_config_details.get('difficulty', 50)
        previous_terrain = previous_config_details.get('terrain_config', {})
        previous_map_dimensions = previous_config_details.get('map_dimensions', {})
        
        terrain_context = f"""
PREVIOUS TERRAIN CONFIGURATION:
• Map Size: {previous_map_dimensions.get('width', 20)}x{previous_map_dimensions.get('height', 15)}
• Terrain Config: {previous_terrain if previous_terrain else 'Standard'}
• Special Mechanics: {previous_config_details.get('special_mechanics', [])}"""
        
        prompt = f"""Create terrain strategy for tower defense map based on player performance and previous terrain configuration.

PLAYER DATA:
- Score: {score}/100
- Won: {win_flag}
- Tower Diversity: {tower_diversity} types

{terrain_context}

DIFFICULTY CONTEXT:
- Player scored {score}% on difficulty {previous_difficulty} level
- Previous map configuration: {previous_map_dimensions.get('width', 20)}x{previous_map_dimensions.get('height', 15)}
- This suggests {'high competence' if score >= 70 and previous_difficulty >= 60 else 'moderate competence' if score >= 50 else 'developing skills'}

Output ONLY this JSON format:
{{
    "path_type": "normal_path",
    "terrain_reasoning": "strategic terrain placement rationale"
}}"""

        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are a terrain designer. Output only JSON, no other text."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=600,  # Increased from 200 for comprehensive terrain strategy analysis
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = ai_response[start_idx:end_idx]
                return json.loads(json_str)
        except Exception as e:
            print(f"🧩⚠️ AI terrain generation failed: {e}")
        
        return None
    
    def _generate_ai_enemy_strategy(self, performance_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate focused AI enemy counter-strategy (small, reliable JSON)"""
        if not self.ai_available:
            return None
        
        score = performance_data.get('score', 50)
        win_flag = performance_data.get('win_flag', False)
        
        # ★ NEW: Use extracted config details from performance data ★
        previous_config_details = performance_data.get('previous_config_details', {})
        previous_difficulty = previous_config_details.get('difficulty', 50)
        previous_enemy_types = previous_config_details.get('enemy_types_used', [])
        previous_wave_systems = previous_config_details.get('wave_systems', {})
        
        enemy_context = f"""
PREVIOUS ENEMY CONFIGURATION:
• Enemy Types Used: {', '.join(previous_enemy_types)}
• Total Enemy Types: {len(previous_enemy_types)}
• Wave Count: {previous_wave_systems.get('total_waves', 80)}
• Boss Waves: {previous_wave_systems.get('boss_waves', 'Standard')}"""
        
        # Get comprehensive difficulty knowledge
        difficulty_70_reference = get_difficulty_70_reference()
        enemy_difficulties = get_enemy_type_difficulty()
        tower_effectiveness = get_tower_effectiveness_matrix()
        
        prompt = f"""Create enemy counter-strategy based on player performance and previous enemy configuration.

COMPREHENSIVE GAME KNOWLEDGE - DIFFICULTY 70 REFERENCE:
This is what difficulty 70 means in our tower defense game:
{json.dumps(difficulty_70_reference, indent=2)}

ENEMY DIFFICULTY RATINGS (1-10 scale):
{json.dumps(enemy_difficulties, indent=2)}

TOWER EFFECTIVENESS MATRIX:
{json.dumps(tower_effectiveness, indent=2)}

PLAYER DATA:
- Score: {score}/100
- Won: {win_flag}

{enemy_context}

DIFFICULTY-ADJUSTED PERFORMANCE ANALYSIS:
- Raw performance: {score}% on difficulty {previous_difficulty}
- Previous enemy types handled: {len(previous_enemy_types)} types
- Adjusted skill level: {'Expert' if score >= 80 and previous_difficulty >= 70 else 'Advanced' if score >= 70 and previous_difficulty >= 50 else 'Intermediate' if score >= 50 and previous_difficulty >= 30 else 'Beginner'}
- Previous challenge level: {'Very Hard' if previous_difficulty >= 80 else 'Hard' if previous_difficulty >= 60 else 'Medium' if previous_difficulty >= 40 else 'Easy'}
- Difficulty 70 comparison: {'Above difficulty 70 reference' if previous_difficulty > 70 else 'At difficulty 70 reference' if previous_difficulty == 70 else 'Below difficulty 70 reference'}

AVAILABLE ENEMIES (you MUST choose only from this list):
Basic Tier: BasicEnemy, FastEnemy, TankEnemy, FlyingEnemy, ShieldedEnemy
Immune Tier: ArmoredEnemy, EnergyShieldEnemy, GroundedEnemy, FireElementalEnemy, ToxicEnemy, PhaseShiftEnemy, BlastProofEnemy
Advanced Tier: InvisibleEnemy, RegeneratingEnemy, TeleportingEnemy, SplittingEnemy, SpectralEnemy, CrystallineEnemy, ToxicMutantEnemy, VoidEnemy, AdaptiveEnemy

🎯 CRITICAL: SELECTIVE ENEMY USAGE REQUIRED
- DO NOT use ALL available enemies in wave compositions
- Use approximately 70% of available enemies (about 15 out of 21 total)
- Focus on strategic enemy selection based on difficulty and counter-strategy
- Leave out 6-7 enemy types to create focused, intentional challenges
- Choose enemies that create specific strategic puzzles rather than overwhelming variety

TOWER COUNTER-STRATEGIES:
- BasicEnemy/TankEnemy → Use against splash reliance (cannon/flame towers)
- FastEnemy → Use against slow precision towers (sniper/laser)
- FlyingEnemy → Use against ground-focused builds (basic/cannon)
- ArmoredEnemy → Immune to basic towers
- GroundedEnemy → Immune to lightning towers
- FireElementalEnemy → Immune to freeze/ice, heals from fire
- EnergyShieldEnemy → Resistant to laser towers
- ToxicEnemy → Immune to poison towers
- PhaseShiftEnemy → Hard to target with precision towers
- SpectralEnemy → Only vulnerable to lightning (requires detection)
- CrystallineEnemy → Only vulnerable to laser towers
- VoidEnemy → Only vulnerable to explosive/missile towers
- TeleportingEnemy → Escapes slow towers and AOE

DIFFICULTY-ADJUSTED PERFORMANCE-BASED COUNTER-STRATEGY RULES:
★ If (Score < 40% OR Previous_Difficulty < 30): NO harsh counter-enemies, focus on supportive challenges
★ If (Score 40-60% AND Previous_Difficulty 30-60): Gentle educational counters (light presence)  
★ If (Score 60%+ AND Previous_Difficulty 40%+): Full counter-strategies to force adaptation
★ If (Score 70%+ AND Previous_Difficulty 70%+): Advanced counter-strategies (player proven skilled on hard content)
★ If (Score 50%+ AND Previous_Difficulty 80%+): Moderate counters (player handled very hard content reasonably)

🎯 EXTREME SPAWN ODDS REQUIREMENTS:
- For LATE GAME waves (60-80): Use VERY EXTREME spawn odds with dramatic differences
- Primary enemies should have 40-70% spawn rates (very high)
- Secondary enemies should have 20-35% spawn rates (moderate)
- Rare enemies should have 1-10% spawn rates (very low)
- Create polarized distributions where some enemies dominate while others are rare
- Later waves should be more extreme than early waves
- Use spawn odds that create focused, intense encounters rather than balanced distributions

EXAMPLE EXTREME DISTRIBUTIONS:
Early waves (1-30): Moderate extremes (20-40% primary, 10-25% secondary, 5-15% rare)
Mid waves (31-60): High extremes (30-50% primary, 15-30% secondary, 2-10% rare)
Late waves (61-80): MAXIMUM extremes (40-70% primary, 20-35% secondary, 1-5% rare)

Output ONLY this JSON format:
{{
    "primary_counter_enemies": ["EnemyName1", "EnemyName2"],
    "strategy_focus": "brief description based on performance level",
    "extreme_spawn_preference": "late_game_focus",
    "spawn_distribution_style": "polarized_extreme"
}}"""

        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are an enemy strategist. Output only JSON, no other text."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,  # Increased from 300 for comprehensive enemy strategy analysis
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = ai_response[start_idx:end_idx]
                return json.loads(json_str)
        except Exception as e:
            print(f"🧩⚠️ AI enemy strategy generation failed: {e}")
        
        return None
    
    def _generate_ai_theme(self, performance_data: Dict[str, Any], 
                          enemy_strategy: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Generate focused AI theme and naming (small, reliable JSON)"""
        if not self.ai_available:
            return None
        
        score = performance_data.get('score', 50)
        previous_difficulty = performance_data.get('previous_difficulty', 50)
        strategy_focus = ""
        if enemy_strategy:
            strategy_focus = enemy_strategy.get('strategy_focus', 'strategic challenge')
        
        prompt = f"""Create creative level name based on strategy and performance.

STRATEGY CONTEXT:
- Player Score: {score}/100
- Previous Level Difficulty: {previous_difficulty}/100
- Strategic Focus: {strategy_focus}

DIFFICULTY PROGRESSION CONTEXT:
- Coming from difficulty {previous_difficulty} where they scored {score}%
- Level name should reflect appropriate challenge escalation

Output ONLY this JSON format:
{{
    "level_name": "Creative Level Name",
    "description": "Brief description of the strategic challenge"
}}"""

        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are a creative game designer. Output only JSON, no other text."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=400,  # Increased from 150 for creative theme generation with detailed reasoning
                temperature=0.9
            )
            
            ai_response = response.choices[0].message.content
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = ai_response[start_idx:end_idx]
                return json.loads(json_str)
        except Exception as e:
            print(f"🧩⚠️ AI theme generation failed: {e}")
        
        return None
    
    def _apply_economic_adjustments(self, config: Dict[str, Any], economic_adjustments: Dict[str, Any]):
        """Apply AI economic adjustments to config, including enemy reward system modifications"""
        
        # ★ ENHANCED: Apply comprehensive economic adjustments with enemy reward control ★
        
        # Apply starting money adjustments
        if 'starting_money_modifier' in economic_adjustments:
            modifier = economic_adjustments['starting_money_modifier']
            if 'progression_config' in config:
                current_money = config['progression_config'].get('starting_money', 500)
                new_money = max(200, min(2000, int(current_money * modifier)))
                config['progression_config']['starting_money'] = new_money
                print(f"💰 Economic AI: Starting money adjusted from ${current_money} to ${new_money} (×{modifier})")
        
        # Apply wave bonus adjustments
        if 'normal_wave_bonus_modifier' in economic_adjustments:
            modifier = economic_adjustments['normal_wave_bonus_modifier']
            if 'wave_config' in config and 'money_config' in config['wave_config']:
                current_bonus = config['wave_config']['money_config'].get('normal_wave_bonus', 50)
                new_bonus = max(20, min(200, int(current_bonus * modifier)))
                config['wave_config']['money_config']['normal_wave_bonus'] = new_bonus
                print(f"🎯 Economic AI: Normal wave bonus adjusted from ${current_bonus} to ${new_bonus} (×{modifier})")
        
        if 'boss_wave_bonus_modifier' in economic_adjustments:
            modifier = economic_adjustments['boss_wave_bonus_modifier']
            if 'wave_config' in config and 'money_config' in config['wave_config']:
                current_bonus = config['wave_config']['money_config'].get('boss_wave_bonus', 200)
                new_bonus = max(100, min(1000, int(current_bonus * modifier)))
                config['wave_config']['money_config']['boss_wave_bonus'] = new_bonus
                print(f"👑 Economic AI: Boss wave bonus adjusted from ${current_bonus} to ${new_bonus} (×{modifier})")
        
        # Apply enemy reward scaling adjustments
        if 'enemy_reward_scaling_modifier' in economic_adjustments:
            modifier = economic_adjustments['enemy_reward_scaling_modifier']
            if 'wave_config' in config and 'enemy_scaling' in config['wave_config']:
                current_scaling = config['wave_config']['enemy_scaling'].get('reward_per_wave', 0.12)
                new_scaling = max(0.05, min(0.25, current_scaling * modifier))
                config['wave_config']['enemy_scaling']['reward_per_wave'] = new_scaling
                print(f"📈 Economic AI: Enemy reward scaling adjusted from {current_scaling:.3f} to {new_scaling:.3f} (×{modifier})")
        
        if 'max_reward_multiplier_modifier' in economic_adjustments:
            modifier = economic_adjustments['max_reward_multiplier_modifier']
            if 'wave_config' in config and 'enemy_scaling' in config['wave_config']:
                current_max = config['wave_config']['enemy_scaling'].get('max_reward_multiplier', 20.0)
                new_max = max(10.0, min(50.0, current_max * modifier))
                config['wave_config']['enemy_scaling']['max_reward_multiplier'] = new_max
                print(f"🔝 Economic AI: Max reward multiplier adjusted from {current_max:.1f} to {new_max:.1f} (×{modifier})")
        
        # ★ NEW: Apply enemy spawn rate adjustments to balance reward distribution ★
        if 'enemy_spawn_rate_adjustments' in economic_adjustments:
            spawn_adjustments = economic_adjustments['enemy_spawn_rate_adjustments']
            if 'wave_config' in config and 'wave_compositions' in config['wave_config']:
                wave_compositions = config['wave_config']['wave_compositions']
                
                for wave_range, enemies in wave_compositions.items():
                    if not isinstance(enemies, list):
                        continue
                    
                    # Modify spawn rates for specific enemies
                    for i, enemy_data in enumerate(enemies):
                        if len(enemy_data) >= 2:
                            enemy_type = enemy_data[0]
                            current_spawn_rate = enemy_data[1]
                            
                            if enemy_type in spawn_adjustments:
                                modifier = spawn_adjustments[enemy_type]
                                new_spawn_rate = max(0.01, min(0.5, current_spawn_rate * modifier))
                                # Fix: Replace the entire entry instead of modifying tuple
                                enemies[i] = [enemy_type, new_spawn_rate]
                                
                                print(f"🎲 Economic AI: {enemy_type} spawn rate adjusted from {current_spawn_rate:.3f} to {new_spawn_rate:.3f} (×{modifier}) in waves {wave_range}")
                
                # ★ EXTREME: Allow more extreme spawn rate distributions ★
                for wave_range, enemies in wave_compositions.items():
                    if isinstance(enemies, list):
                        total_spawn_rate = sum(enemy_data[1] for enemy_data in enemies if len(enemy_data) >= 2)
                        if total_spawn_rate > 0:
                            # Only normalize if total exceeds 3.0 (allow more extreme distributions)
                            if total_spawn_rate > 3.0:
                                normalization_factor = 3.0 / total_spawn_rate
                                for i, enemy_data in enumerate(enemies):
                                    if len(enemy_data) >= 2:
                                        # Fix: Replace the entire entry instead of modifying tuple
                                        enemies[i] = [enemy_data[0], enemy_data[1] * normalization_factor]
                                print(f"🔄 Economic AI: Normalized spawn rates in waves {wave_range} (total was {total_spawn_rate:.2f})")
                            else:
                                print(f"🎲 Economic AI: Preserved extreme spawn rates in waves {wave_range} (total: {total_spawn_rate:.2f})")
        
        # Apply strategic focus adjustments
        if 'strategic_focus' in economic_adjustments:
            focus = economic_adjustments['strategic_focus']
            
            # Store strategic focus in config metadata for tracking
            if '_ai_metadata' not in config:
                config['_ai_metadata'] = {}
            config['_ai_metadata']['economic_focus'] = focus
            config['_ai_metadata']['economic_reasoning'] = economic_adjustments.get('reasoning', 'AI economic adjustments applied')
            
            print(f"🎯 Economic AI: Strategic focus set to '{focus}'")
            if 'reasoning' in economic_adjustments:
                print(f"💡 Economic AI: Reasoning - {economic_adjustments['reasoning']}")
        
        # Store original values for comparison if not already stored
        if '_original_economic_values' not in config:
            config['_original_economic_values'] = {
                'starting_money': config.get('progression_config', {}).get('starting_money', 500),
                'normal_wave_bonus': config.get('wave_config', {}).get('money_config', {}).get('normal_wave_bonus', 50),
                'boss_wave_bonus': config.get('wave_config', {}).get('money_config', {}).get('boss_wave_bonus', 200),
                'reward_per_wave': config.get('wave_config', {}).get('enemy_scaling', {}).get('reward_per_wave', 0.12),
                'max_reward_multiplier': config.get('wave_config', {}).get('enemy_scaling', {}).get('max_reward_multiplier', 20.0)
            }
        
        # ★ NEW: Apply tower cost progression adjustments for dramatic scaling ★
        if 'tower_cost_progression' in economic_adjustments:
            cost_progression = economic_adjustments['tower_cost_progression']
            
            # Apply to tower config in game config
            if 'tower_config' in config:
                tower_config = config['tower_config']
                if 'cost_progression' not in tower_config:
                    # Initialize with default values if not present
                    tower_config['cost_progression'] = {
                        'early_game_waves': 15,
                        'mid_game_waves': 30,
                        'early_increase_per_wave': 0.02,
                        'mid_increase_per_wave': 0.03,
                        'late_increase_per_wave': 0.05,
                        'max_cost_multiplier': 3.0
                    }
                
                cost_config = tower_config['cost_progression']
                
                # Apply dramatic cost scaling modifiers
                if 'early_increase_per_wave_modifier' in cost_progression:
                    modifier = cost_progression['early_increase_per_wave_modifier']
                    old_value = cost_config.get('early_increase_per_wave', 0.02)
                    new_value = max(0.01, min(0.08, old_value * modifier))  # 1-8% per wave
                    cost_config['early_increase_per_wave'] = new_value
                    print(f"🏗️ Tower Cost AI: Early cost increase adjusted from {old_value*100:.1f}% to {new_value*100:.1f}% per wave (×{modifier})")
                
                if 'mid_increase_per_wave_modifier' in cost_progression:
                    modifier = cost_progression['mid_increase_per_wave_modifier']
                    old_value = cost_config.get('mid_increase_per_wave', 0.03)
                    new_value = max(0.01, min(0.12, old_value * modifier))  # 1-12% per wave
                    cost_config['mid_increase_per_wave'] = new_value
                    print(f"🏗️ Tower Cost AI: Mid cost increase adjusted from {old_value*100:.1f}% to {new_value*100:.1f}% per wave (×{modifier})")
                
                if 'late_increase_per_wave_modifier' in cost_progression:
                    modifier = cost_progression['late_increase_per_wave_modifier']
                    old_value = cost_config.get('late_increase_per_wave', 0.05)
                    new_value = max(0.02, min(0.20, old_value * modifier))  # 2-20% per wave
                    cost_config['late_increase_per_wave'] = new_value
                    print(f"🏗️ Tower Cost AI: Late cost increase adjusted from {old_value*100:.1f}% to {new_value*100:.1f}% per wave (×{modifier})")
                
                if 'max_cost_multiplier_modifier' in cost_progression:
                    modifier = cost_progression['max_cost_multiplier_modifier']
                    old_value = cost_config.get('max_cost_multiplier', 3.0)
                    new_value = max(2.0, min(15.0, old_value * modifier))  # 2x-15x max multiplier
                    cost_config['max_cost_multiplier'] = new_value
                    print(f"🏗️ Tower Cost AI: Max cost multiplier adjusted from {old_value:.1f}x to {new_value:.1f}x (×{modifier})")
                
                # Calculate and display the dramatic scaling impact
                print(f"🏗️ DRAMATIC Tower Cost Scaling Applied:")
                print(f"    📈 Early waves (1-15): {cost_config.get('early_increase_per_wave', 0.02)*100:.1f}% cost increase per wave")
                print(f"    📈 Mid waves (16-30): {cost_config.get('mid_increase_per_wave', 0.03)*100:.1f}% cost increase per wave")
                print(f"    📈 Late waves (31+): {cost_config.get('late_increase_per_wave', 0.05)*100:.1f}% cost increase per wave")
                print(f"    🔝 Maximum cost multiplier: {cost_config.get('max_cost_multiplier', 3.0):.1f}x")
                
                # Calculate late-game cost example
                late_increase = cost_config.get('late_increase_per_wave', 0.05)
                max_multiplier = cost_config.get('max_cost_multiplier', 3.0)
                wave_80_multiplier = min(max_multiplier, 1.0 + (79 * late_increase))  # Rough calculation
                print(f"    💰 Example: Wave 80 tower cost multiplier ≈ {wave_80_multiplier:.1f}x (${20 * wave_80_multiplier:.0f} for basic tower)")
            
            # Also apply to static tower config if present for consistency
            elif 'static_tower_config' in config:
                static_config = config['static_tower_config']
                if 'cost_progression' in static_config:
                    # Apply same logic to static config
                    cost_config = static_config['cost_progression']
                    
                    if 'early_increase_per_wave_modifier' in cost_progression:
                        modifier = cost_progression['early_increase_per_wave_modifier']
                        old_value = cost_config.get('early_increase_per_wave', 0.02)
                        new_value = max(0.01, min(0.08, old_value * modifier))
                        cost_config['early_increase_per_wave'] = new_value
                    
                    if 'mid_increase_per_wave_modifier' in cost_progression:
                        modifier = cost_progression['mid_increase_per_wave_modifier']
                        old_value = cost_config.get('mid_increase_per_wave', 0.03)
                        new_value = max(0.01, min(0.12, old_value * modifier))
                        cost_config['mid_increase_per_wave'] = new_value
                    
                    if 'late_increase_per_wave_modifier' in cost_progression:
                        modifier = cost_progression['late_increase_per_wave_modifier']
                        old_value = cost_config.get('late_increase_per_wave', 0.05)
                        new_value = max(0.02, min(0.20, old_value * modifier))
                        cost_config['late_increase_per_wave'] = new_value
                    
                    if 'max_cost_multiplier_modifier' in cost_progression:
                        modifier = cost_progression['max_cost_multiplier_modifier']
                        old_value = cost_config.get('max_cost_multiplier', 3.0)
                        new_value = max(2.0, min(15.0, old_value * modifier))
                        cost_config['max_cost_multiplier'] = new_value
                    
                    print(f"🏗️ Tower Cost AI: Applied dramatic cost scaling to static tower config")


    
    def _apply_wave_adjustments(self, config: Dict[str, Any], wave_adjustments: Dict[str, Any]):
        """Apply AI wave progression adjustments to config using PREVIOUS LEVEL DATA as base"""
        if 'wave_config' not in config:
            return
        
        # ★ FIXED: Use previous level's wave count as base, not base config ★
        if 'total_waves_modifier' in wave_adjustments:
            # Try to get previous level's actual wave count from performance data
            previous_waves = 80  # Fallback default
            
            # Look for previous config data stored in the config metadata
            if '_previous_level_data' in config:
                previous_waves = config['_previous_level_data'].get('total_waves', 80)
            elif '_ai_wave_strategy' in config and 'previous_total_waves' in config['_ai_wave_strategy']:
                previous_waves = config['_ai_wave_strategy']['previous_total_waves']
            else:
                # Last resort: use current config (old behavior)
                previous_waves = config['wave_config'].get('total_waves', 80)
            
            new_waves = max(10, min(120, int(previous_waves * wave_adjustments['total_waves_modifier'])))
            config['wave_config']['total_waves'] = new_waves
            print(f"   📊 Total Waves: {previous_waves} (previous) → {new_waves}")
        
        # Apply enemy scaling modifications
        if 'enemy_scaling' in config['wave_config']:
            enemy_scaling = config['wave_config']['enemy_scaling']
            
            # ★ NEW: Apply all per-wave scaling modifiers ★
            if 'enemy_health_scaling_modifier' in wave_adjustments:
                old_health = enemy_scaling.get('health_per_wave', 0.3)
                new_health = old_health * wave_adjustments['enemy_health_scaling_modifier']
                enemy_scaling['health_per_wave'] = new_health
                print(f"   💪 Health Scaling: {old_health:.2f} → {new_health:.2f}")
            
            if 'enemy_speed_scaling_modifier' in wave_adjustments:
                old_speed = enemy_scaling.get('speed_per_wave', 0.05)
                new_speed = old_speed * wave_adjustments['enemy_speed_scaling_modifier']
                enemy_scaling['speed_per_wave'] = new_speed
                print(f"   🏃 Speed Scaling: {old_speed:.3f} → {new_speed:.3f}")
            
            if 'enemy_reward_scaling_modifier' in wave_adjustments:
                old_reward = enemy_scaling.get('reward_per_wave', 0.12)
                new_reward = old_reward * wave_adjustments['enemy_reward_scaling_modifier']
                enemy_scaling['reward_per_wave'] = new_reward
                print(f"   💰 Reward Scaling: {old_reward:.3f} → {new_reward:.3f}")
            
            if 'enemy_size_scaling_modifier' in wave_adjustments:
                old_size = enemy_scaling.get('size_per_wave', 0.02)
                new_size = old_size * wave_adjustments['enemy_size_scaling_modifier']
                enemy_scaling['size_per_wave'] = new_size
                print(f"   📏 Size Scaling: {old_size:.3f} → {new_size:.3f}")
            
            if 'enemy_damage_scaling_modifier' in wave_adjustments:
                old_damage = enemy_scaling.get('damage_scaling_per_wave', 0.1)
                new_damage = old_damage * wave_adjustments['enemy_damage_scaling_modifier']
                enemy_scaling['damage_scaling_per_wave'] = new_damage
                print(f"   ⚔️ Damage Scaling: {old_damage:.3f} → {new_damage:.3f}")
            
            # ★ NEW: Apply all max multiplier cap modifiers ★
            if 'max_health_multiplier_modifier' in wave_adjustments:
                old_max_health = enemy_scaling.get('max_health_multiplier', 60.0)
                new_max_health = old_max_health * wave_adjustments['max_health_multiplier_modifier']
                enemy_scaling['max_health_multiplier'] = new_max_health
                print(f"   💪📈 Max Health Cap: {old_max_health:.1f}x → {new_max_health:.1f}x")
            
            if 'max_speed_multiplier_modifier' in wave_adjustments:
                old_max_speed = enemy_scaling.get('max_speed_multiplier', 5.0)
                new_max_speed = old_max_speed * wave_adjustments['max_speed_multiplier_modifier']
                enemy_scaling['max_speed_multiplier'] = new_max_speed
                print(f"   🏃📈 Max Speed Cap: {old_max_speed:.1f}x → {new_max_speed:.1f}x")
            
            if 'max_reward_multiplier_modifier' in wave_adjustments:
                old_max_reward = enemy_scaling.get('max_reward_multiplier', 20.0)
                new_max_reward = old_max_reward * wave_adjustments['max_reward_multiplier_modifier']
                enemy_scaling['max_reward_multiplier'] = new_max_reward
                print(f"   💰📈 Max Reward Cap: {old_max_reward:.1f}x → {new_max_reward:.1f}x")
            
            if 'max_size_multiplier_modifier' in wave_adjustments:
                old_max_size = enemy_scaling.get('max_size_multiplier', 2.0)
                new_max_size = old_max_size * wave_adjustments['max_size_multiplier_modifier']
                enemy_scaling['max_size_multiplier'] = new_max_size
                print(f"   📏📈 Max Size Cap: {old_max_size:.1f}x → {new_max_size:.1f}x")
            
            if 'max_damage_multiplier_modifier' in wave_adjustments:
                old_max_damage = enemy_scaling.get('max_damage_multiplier', 4.0)
                new_max_damage = old_max_damage * wave_adjustments['max_damage_multiplier_modifier']
                enemy_scaling['max_damage_multiplier'] = new_max_damage
                print(f"   ⚔️📈 Max Damage Cap: {old_max_damage:.1f}x → {new_max_damage:.1f}x")
        
        # ★ NEW: Apply spawn rate modifications ★
        if 'spawn_config' in config['wave_config']:
            spawn_config = config['wave_config']['spawn_config']
            
            if 'min_spawn_delay_modifier' in wave_adjustments:
                old_min_delay = spawn_config.get('min_spawn_delay', 0.1)
                new_min_delay = old_min_delay * wave_adjustments['min_spawn_delay_modifier']
                spawn_config['min_spawn_delay'] = new_min_delay
                print(f"   ⏱️ Min Spawn Delay: {old_min_delay:.2f} → {new_min_delay:.2f}")
            
            if 'base_spawn_delay_modifier' in wave_adjustments:
                old_base_delay = spawn_config.get('base_spawn_delay', 120)
                new_base_delay = int(old_base_delay * wave_adjustments['base_spawn_delay_modifier'])
                spawn_config['base_spawn_delay'] = new_base_delay
                print(f"   ⏱️ Base Spawn Delay: {old_base_delay} → {new_base_delay}")
        
        # ★ NEW: Apply max enemy health and speed modifiers for base stats ★
        if 'enemy_max_health_modifier' in wave_adjustments or 'enemy_max_speed_modifier' in wave_adjustments:
            if 'enemy_buffs' not in config:
                config['enemy_buffs'] = {}
            
            if 'enemy_max_health_modifier' in wave_adjustments:
                health_modifier = wave_adjustments['enemy_max_health_modifier']
                config['enemy_buffs']['max_health_modifier'] = health_modifier
                print(f"   ❤️ Max Health Modifier: {health_modifier:.2f}x (affects all enemy base health)")
            
            if 'enemy_max_speed_modifier' in wave_adjustments:
                speed_modifier = wave_adjustments['enemy_max_speed_modifier']
                config['enemy_buffs']['max_speed_modifier'] = speed_modifier
                print(f"   🏃 Max Speed Modifier: {speed_modifier:.2f}x (affects all enemy base speed)")
        
        # Apply spawn delay reduction modifications
        if 'spawn_delay_reduction_modifier' in wave_adjustments and 'round_progression' in config['wave_config']:
            round_prog = config['wave_config']['round_progression']
            if 'spawn_delay_reduction_per_round' in round_prog:
                delay_config = round_prog['spawn_delay_reduction_per_round']
                modifier = wave_adjustments['spawn_delay_reduction_modifier']
                
                # Apply to wave ranges
                if 'wave_ranges' in delay_config:
                    for wave_range, value in delay_config['wave_ranges'].items():
                        delay_config['wave_ranges'][wave_range] = int(value * modifier)
                
                # Apply to default
                if 'default' in delay_config:
                    delay_config['default'] = int(delay_config['default'] * modifier)
                
                print(f"   ⏱️ Spawn Delay Reduction: Modified by {modifier:.2f}x")
        
        # Store AI wave strategy
        config['_ai_wave_strategy'] = wave_adjustments
    
    def _apply_terrain_strategy(self, config: Dict[str, Any], terrain_strategy: Dict[str, Any]):
        """Apply AI terrain strategy to procedural config"""
        config['_ai_terrain_strategy'] = terrain_strategy
        print(f"   🌍 Terrain: {terrain_strategy.get('terrain_reasoning', 'AI terrain strategy applied')}")
    
    def _apply_enemy_strategy(self, config: Dict[str, Any], enemy_strategy: Dict[str, Any]):
        """Apply AI enemy counter-strategy to wave compositions with EXTREME spawn odds"""
        config['_ai_enemy_strategy'] = enemy_strategy
        
        primary_enemies = enemy_strategy.get('primary_counter_enemies', [])
        spawn_style = enemy_strategy.get('spawn_distribution_style', 'balanced')
        extreme_preference = enemy_strategy.get('extreme_spawn_preference', 'moderate')
        
        if primary_enemies and 'wave_config' in config and 'wave_compositions' in config['wave_config']:
            wave_compositions = config['wave_config']['wave_compositions']
            
            # PROTECTION: Never modify the standardized first 5 waves
            protected_ranges = ["1-5"]
            
            for wave_range, compositions in wave_compositions.items():
                # PROTECTION: Skip standardized first 5 waves
                if wave_range in protected_ranges:
                    print(f"   🛡️ PROTECTED: Skipping waves {wave_range} (standardized configuration)")
                    continue
                
                # ★ NEW: Create EXTREME spawn distributions based on wave range ★
                wave_start = int(wave_range.split('-')[0])
                wave_end = int(wave_range.split('-')[1])
                avg_wave = (wave_start + wave_end) / 2
                
                # Determine extreme level based on wave range
                if avg_wave >= 60:  # Late game - MAXIMUM extremes
                    primary_spawn_rate = 0.55  # 55% for primary enemies
                    secondary_spawn_rate = 0.25  # 25% for secondary enemies
                    rare_spawn_rate = 0.03  # 3% for rare enemies
                    extreme_level = "MAXIMUM"
                elif avg_wave >= 30:  # Mid game - High extremes
                    primary_spawn_rate = 0.45  # 45% for primary enemies
                    secondary_spawn_rate = 0.20  # 20% for secondary enemies
                    rare_spawn_rate = 0.06  # 6% for rare enemies
                    extreme_level = "HIGH"
                else:  # Early game - Moderate extremes
                    primary_spawn_rate = 0.35  # 35% for primary enemies
                    secondary_spawn_rate = 0.15  # 15% for secondary enemies
                    rare_spawn_rate = 0.10  # 10% for rare enemies
                    extreme_level = "MODERATE"
                
                # Convert to dict for easier manipulation
                comp_dict = {enemy: rate for enemy, rate in compositions}
                all_enemies = list(comp_dict.keys())
                
                # Create extreme distribution
                new_comp_dict = {}
                
                # Assign primary enemies (high spawn rates)
                for i, enemy in enumerate(primary_enemies):
                    if enemy in all_enemies:
                        new_comp_dict[enemy] = primary_spawn_rate / len(primary_enemies)
                    else:
                        # Add new primary enemy if not present
                        new_comp_dict[enemy] = primary_spawn_rate / len(primary_enemies)
                
                # Assign secondary enemies (moderate spawn rates)
                remaining_enemies = [e for e in all_enemies if e not in primary_enemies]
                if remaining_enemies:
                    # Take up to 2-3 secondary enemies
                    secondary_count = min(3, len(remaining_enemies))
                    secondary_enemies = remaining_enemies[:secondary_count]
                    
                    for enemy in secondary_enemies:
                        new_comp_dict[enemy] = secondary_spawn_rate / len(secondary_enemies)
                    
                    # Assign rare enemies (very low spawn rates)
                    rare_enemies = remaining_enemies[secondary_count:]
                    if rare_enemies:
                        remaining_budget = 1.0 - sum(new_comp_dict.values())
                        if remaining_budget > 0:
                            for enemy in rare_enemies:
                                new_comp_dict[enemy] = min(rare_spawn_rate, remaining_budget / len(rare_enemies))
                
                # ★ EXTREME: Allow spawn rates to exceed 1.0 for dramatic effect, then normalize ★
                total = sum(new_comp_dict.values())
                if total > 1.0:
                    # Preserve extreme ratios but normalize to 1.0
                    for enemy in new_comp_dict:
                        new_comp_dict[enemy] = new_comp_dict[enemy] / total
                
                # ★ EXTREME: Remove enemies with very low spawn rates to create focus ★
                final_comp_dict = {}
                for enemy, rate in new_comp_dict.items():
                    if rate >= 0.02:  # Only keep enemies with 2%+ spawn rate
                        final_comp_dict[enemy] = rate
                
                # Re-normalize after filtering
                total = sum(final_comp_dict.values())
                if total > 0:
                    for enemy in final_comp_dict:
                        final_comp_dict[enemy] = final_comp_dict[enemy] / total
                
                # Convert back to list format
                config['wave_config']['wave_compositions'][wave_range] = [
                    [enemy, rate] for enemy, rate in final_comp_dict.items()
                ]
                
                print(f"   🎯 {extreme_level} EXTREME Enemy Strategy: Waves {wave_range} - Primary: {primary_enemies} at {primary_spawn_rate*100:.0f}%+ spawn rate")
        
        print(f"   🎲 Extreme Spawn Distribution: {spawn_style} style with {extreme_preference} preference applied")
    
    def _generate_ai_buff_system_config(self, performance_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate AI-designed buff system configuration based on performance analysis"""
        try:
            # Check if we have buff performance data available (handle both dict and object formats)
            has_buff_data = (
                performance_data.get('buff_encounters') or
                performance_data.get('enemy_buff_intensity', 'none') != 'none' or
                performance_data.get('buff_adaptation_score', 0) > 0
            )
            
            # If we have direct buff performance data, use the comprehensive analysis
            if has_buff_data:
                print("   🎭 Using buff performance data for AI config generation")
                
                # Convert dict to PerformanceData object for the analysis functions
                from .performance_analysis import PerformanceData
                
                # Create PerformanceData object from dict
                perf_obj = PerformanceData(
                    win_flag=performance_data.get('win_flag', False),
                    lives_remaining=performance_data.get('lives_remaining', 0),
                    starting_lives=performance_data.get('starting_lives', 20),
                    towers_built=performance_data.get('towers_built', {}),
                    tower_diversity=performance_data.get('tower_diversity', 0),
                    wave_reached=performance_data.get('wave_reached', performance_data.get('score', 50)),
                    final_wave=performance_data.get('final_wave', 80),
                    # Buff system metrics
                    buff_encounters=performance_data.get('buff_encounters'),
                    buff_combinations_seen=performance_data.get('buff_combinations_seen'),
                    most_challenging_buffs=performance_data.get('most_challenging_buffs'),
                    tower_counter_effectiveness=performance_data.get('tower_counter_effectiveness'),
                    buff_adaptation_score=performance_data.get('buff_adaptation_score', 0.0),
                    enemy_buff_intensity=performance_data.get('enemy_buff_intensity', 'none'),
                    buff_related_deaths=performance_data.get('buff_related_deaths', 0),
                    effective_counter_strategies=performance_data.get('effective_counter_strategies')
                )
                
                # Generate buff config using performance analysis
                buff_config = generate_buff_config_from_performance([perf_obj])
                
                # Add AI-specific enhancements
                if self.ai_available:
                    ai_enhancements = self._generate_ai_buff_enhancements(performance_data)
                    if ai_enhancements:
                        # Merge AI enhancements with performance-based config
                        buff_config.update(ai_enhancements)
                        print("   🤖 AI buff enhancements applied")
                
                return buff_config
            
            # If no buff data available, generate basic configuration based on general performance
            else:
                print("   🎭 Generating buff config from general performance data")
                return self._generate_fallback_buff_config(performance_data)
                
        except Exception as e:
            print(f"   ⚠️ Buff system generation failed: {e}")
            return None
    
    def _generate_ai_buff_enhancements(self, performance_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate AI-specific buff system enhancements"""
        if not self.ai_available:
            return None
        
        score = performance_data.get('score', 50)
        win_flag = performance_data.get('win_flag', False)
        adaptation_score = performance_data.get('buff_adaptation_score', 0)
        challenging_buffs = performance_data.get('most_challenging_buffs', [])
        
        prompt = f"""Enhance buff system configuration based on detailed player performance.

PLAYER BUFF PERFORMANCE:
- Overall Score: {score}/100
- Won Game: {win_flag}
- Buff Adaptation Score: {adaptation_score}/100
- Most Challenging Buffs: {challenging_buffs}
- Buff Encounters: {performance_data.get('buff_encounters', {})}

Based on this performance, provide buff system enhancements:

{{
    "intensity_override": "low|medium|high|extreme",
    "focus_buffs": ["list", "of", "buffs", "to", "emphasize"],
    "avoid_buffs": ["list", "of", "buffs", "to", "reduce"],
    "special_combinations": ["list", "of", "combinations", "to", "feature"],
    "spawn_rate_adjustments": {{
        "early_game_modifier": 0.5-2.0,
        "late_game_modifier": 0.5-2.0
    }},
    "reasoning": "explanation of enhancements"
}}"""

        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are a buff system designer. Output only JSON, no other text."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=600,
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = ai_response[start_idx:end_idx]
                return json.loads(json_str)
        except Exception as e:
            print(f"   ⚠️ AI buff enhancements failed: {e}")
        
        return None
    
    def _generate_fallback_buff_config(self, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate fallback buff configuration when no buff performance data is available"""
        score = performance_data.get('score', 50)
        win_flag = performance_data.get('win_flag', False)
        
        # Determine intensity based on general performance
        if win_flag and score >= 80:
            intensity = "high"
            base_chance_multiplier = 1.4
        elif win_flag and score >= 60:
            intensity = "medium"
            base_chance_multiplier = 1.0
        elif win_flag:
            intensity = "low"
            base_chance_multiplier = 0.7
        else:
            intensity = "low"
            base_chance_multiplier = 0.5
        
        # 🎯 SELECTIVE BUFF USAGE: Use only ~70% of available buffs (8 out of 12)
        # Available buffs: speed_boost, armor, invisibility, regeneration, flying, anti_explosive, 
        # spell_resistance, fire_immunity, poison_immunity, freeze_immunity, berserker, phase_shift
        core_buffs = ["speed_boost", "armor", "invisibility", "regeneration", "flying", "anti_explosive"]
        advanced_buffs = ["spell_resistance", "berserker"]  # Only 2 out of 6 advanced buffs
        
        # Generate basic wave progression
        wave_ranges = {
            "1-10": {
                "base_chance": min(0.15, 0.05 * base_chance_multiplier),
                "max_buffs": 1,
                "allowed_buffs": ["speed_boost", "armor"]
            },
            "11-20": {
                "base_chance": min(0.3, 0.15 * base_chance_multiplier),
                "max_buffs": 2,
                "allowed_buffs": core_buffs[:4]  # First 4 core buffs
            },
            "21-30": {
                "base_chance": min(0.45, 0.25 * base_chance_multiplier),
                "max_buffs": 3,
                "allowed_buffs": core_buffs  # All 6 core buffs
            },
            "31-40": {
                "base_chance": min(0.6, 0.35 * base_chance_multiplier),
                "max_buffs": 4,
                "allowed_buffs": core_buffs + advanced_buffs[:1]  # Core + 1 advanced
            },
            "41-50": {
                "base_chance": min(0.75, 0.45 * base_chance_multiplier),
                "max_buffs": 5,
                "allowed_buffs": core_buffs + advanced_buffs  # Core + 2 advanced (8 total)
            },
            "51+": {
                "base_chance": min(0.9, 0.6 * base_chance_multiplier),
                "max_buffs": min(6, int(5 * base_chance_multiplier)),
                "allowed_buffs": core_buffs + advanced_buffs  # 8 buffs max (70% of 12)
            }
        }
        
        # Select featured combinations based on intensity (only using available buffs)
        featured_combinations = ["stealth_assassin", "flying_fortress"]
        if intensity in ["high", "extreme"]:
            featured_combinations.extend(["regenerating_tank"])
        # Note: Limited combinations due to 70% buff usage policy
        
        return {
            "description": "AI-generated buff configuration based on general performance",
            "enabled": True,
            "scenario_type": "adaptive",
            "buff_intensity": intensity,
            "custom_spawn_rates": {
                "wave_ranges": wave_ranges,
                "boss_multipliers": {
                    "mini_boss": 2.0 * base_chance_multiplier,
                    "boss": 3.0 * base_chance_multiplier,
                    "super_boss": 4.0 * base_chance_multiplier
                }
            },
            "featured_combinations": featured_combinations,
            "buff_metrics_tracking": {
                "track_buff_effectiveness": True,
                "track_tower_counters": True,
                "track_player_adaptation": True
            },
            "generation_metadata": {
                "generated_from": "general_performance",
                "intensity_reasoning": f"Based on {score}% score and {'win' if win_flag else 'loss'}",
                "fallback_config": True
            }
        }
    
    def _generate_ai_map_structure(self, performance_data: Dict[str, Any], width: int, height: int) -> Optional[Dict[str, Any]]:
        """Generate AI-designed map structure and layout strategy"""
        if not self.ai_available:
            return None
        
        score = performance_data.get('score', 50)
        win_flag = performance_data.get('win_flag', False)
        previous_difficulty = performance_data.get('previous_difficulty', 50)
        tower_diversity = performance_data.get('tower_diversity', 3)

        # Enhanced: Get reference config context
        is_reference_config = performance_data.get('is_reference_config', False)
        reference_difficulty = performance_data.get('reference_difficulty', previous_difficulty)
        
        # Get enhanced multi-game context if available
        multi_game_context = performance_data.get('multi_game_context', {})
        avg_score = multi_game_context.get('avg_score', score) if multi_game_context else score
        trend = multi_game_context.get('trend', 'stable') if multi_game_context else 'stable'
        difficulty_progression = multi_game_context.get('difficulty_progression', []) if multi_game_context else []
        strategic_patterns = multi_game_context.get('strategic_patterns', {}) if multi_game_context else {}
        performance_trends = multi_game_context.get('performance_trends', {}) if multi_game_context else {}

        # Format difficulty progression for display
        difficulty_progression_str = " → ".join(map(str, difficulty_progression)) if difficulty_progression else "No progression data"

        # Extract strategic insights
        preferred_strategy = strategic_patterns.get('most_preferred_strategy', 'unknown')
        tower_effectiveness = strategic_patterns.get('tower_effectiveness', {})
        economic_patterns = strategic_patterns.get('economic_patterns', {})

        # Extract performance insights
        score_trend = performance_trends.get('score_trend', 'stable')
        problem_areas = performance_trends.get('problem_areas', [])
        strengths = performance_trends.get('strengths', [])
        consistency = performance_trends.get('consistency', 0.5)
        
        # Get comprehensive difficulty knowledge
        difficulty_70_reference = get_difficulty_70_reference()
        difficulty_components = understand_difficulty_components()
        
        prompt = f"""Design complete map structure and layout strategy for tower defense based on player performance.

COMPREHENSIVE GAME KNOWLEDGE - DIFFICULTY 70 REFERENCE:
This is what difficulty 70 means in our tower defense game:
{json.dumps(difficulty_70_reference, indent=2)}

DIFFICULTY COMPONENTS UNDERSTANDING:
{json.dumps(difficulty_components, indent=2)}

PLAYER PERFORMANCE ANALYSIS:
- Current Score: {score}/100
- Current Win: {win_flag}
- Previous Level Difficulty: {previous_difficulty}/100{'  🎯 (REFERENCE CONFIG - tower_defense_game.json)' if is_reference_config else ''}
- Multi-Game Average: {avg_score:.1f}/100
- Performance Trend: {trend}
- Tower Diversity Used: {tower_diversity} types

REFERENCE CONFIG CONTEXT:
- Previous config was {'the REFERENCE tower_defense_game.json (difficulty 70)' if is_reference_config else f'a custom config (difficulty {previous_difficulty})'}
- This means the player {'has experience with the baseline difficulty 70 challenge' if is_reference_config else f'has experience with difficulty {previous_difficulty} relative to the baseline'}
- Use difficulty 70 as the foundation for understanding what constitutes appropriate challenge levels

MULTI-GAME STRATEGIC ANALYSIS:
- Recent Difficulty Progression: {difficulty_progression_str}
- Difficulty Trend: {'progressive increase' if len(difficulty_progression) >= 3 and difficulty_progression[0] > difficulty_progression[-1] else 'progressive decrease' if len(difficulty_progression) >= 3 and difficulty_progression[0] < difficulty_progression[-1] else 'stable difficulty'} over {len(difficulty_progression)} games
- Difficulty Range Experienced: {min(difficulty_progression) if difficulty_progression else 'unknown'}-{max(difficulty_progression) if difficulty_progression else 'unknown'}

STRATEGIC PATTERNS IDENTIFIED:
- Preferred Strategy: {preferred_strategy}
- Strategy Consistency: {strategic_patterns.get('strategy_consistency', 0.0):.1%}
- Tower Effectiveness: {list(tower_effectiveness.keys())[:3] if tower_effectiveness else 'No data'}
- Economic Efficiency: {economic_patterns.get('avg_economic_efficiency', 0.0):.2f}
- Resource Management: {economic_patterns.get('avg_resource_management', 0.0):.1f}/100

PERFORMANCE TRENDS ANALYSIS:
- Score Trend: {score_trend}
- Performance Consistency: {consistency:.1%}
- Problem Areas: {', '.join(problem_areas) if problem_areas else 'None identified'}
- Strengths: {', '.join(strengths) if strengths else 'None identified'}
- Win Rate: {performance_trends.get('win_rate', 0.0):.1f}%

DIFFICULTY CONTEXT:
- Player scored {score}% on difficulty {previous_difficulty} level
- This indicates {'Expert skill' if score >= 80 and previous_difficulty >= 70 else 'Advanced skill' if score >= 70 and previous_difficulty >= 50 else 'Intermediate skill' if score >= 50 and previous_difficulty >= 30 else 'Beginner skill'} level
- Previous challenge was {'Very Hard' if previous_difficulty >= 80 else 'Hard' if previous_difficulty >= 60 else 'Medium' if previous_difficulty >= 40 else 'Easy'}
- Difficulty 70 comparison: {'Above difficulty 70 reference' if previous_difficulty > 70 else 'At difficulty 70 reference' if previous_difficulty == 70 else 'Below difficulty 70 reference'}

MAP DESIGN PRINCIPLES:
- Easy: Long paths, high buildable space (80%+), simple routing for learning
- Medium: Moderate paths, balanced buildable space (60%), some strategic constraints  
- Hard: Short paths, limited buildable space (35%), complex strategic decisions

PATH STRATEGY OPTIONS:
- "direct": Shortest path, high time pressure, forces quick decisions
- "winding": Medium path with turns, moderate time pressure
- "maze": Long path with complex routing, lower time pressure, learning focus

TERRAIN STRATEGY OPTIONS:
- "open": Maximum buildable space, simple tower placement decisions
- "balanced": Mixed terrain requiring strategic tower type selection
- "constrained": Limited buildable space forcing specialized strategies
- "chokepoint": Strategic bottlenecks requiring careful placement

BUILDABLE SPACE TARGETS:
- Beginner: 80%+ buildable (forgiving, learning-focused)
- Intermediate: 60% buildable (balanced challenge)
- Advanced: 35% buildable (strategic constraints)

STRATEGIC RECOMMENDATIONS BASED ON MULTI-GAME ANALYSIS:
- If player prefers {preferred_strategy} strategy: {'Design maps that support this strategy' if preferred_strategy != 'unknown' else 'No clear strategy preference detected'}
- If player has {', '.join(problem_areas) if problem_areas else 'no major issues'}: {'Address these weaknesses in map design' if problem_areas else 'Maintain current challenge level'}
- If player shows {', '.join(strengths) if strengths else 'developing skills'}: {'Build upon these strengths' if strengths else 'Focus on skill development'}
- Economic efficiency is {economic_patterns.get('avg_economic_efficiency', 0.0):.2f}: {'Provide economic challenges' if economic_patterns.get('avg_economic_efficiency', 0.0) > 1.5 else 'Provide economic support' if economic_patterns.get('avg_economic_efficiency', 0.0) < 1.0 else 'Maintain economic balance'}

MAP DIMENSIONS: {width}x{height}

Based on the comprehensive multi-game analysis above, design a map that addresses the player's patterns and trends.

Output ONLY this JSON format:
{{
    "path_strategy": "direct|winding|maze",
    "path_length_target": 25-60,
    "path_complexity": 0.1-0.9,
    "terrain_strategy": "open|balanced|constrained|chokepoint",
    "buildable_space_target": 0.35-0.85,
    "strategic_focus": "learning|adaptation|optimization|mastery",
    "layout_reasoning": "explanation of map design choices based on player skill level"
}}"""

        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are a map design expert. Output only JSON, no other text."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,  # Increased from 400 for comprehensive map structure analysis
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = ai_response[start_idx:end_idx]
                return json.loads(json_str)
        except Exception as e:
            print(f"🧩⚠️ AI map structure generation failed: {e}")
        
        return None
    
    def _generate_config_with_ai_strategy(self, difficulty: int, width: int, height: int, 
                                        total_waves: int, map_strategy: Dict[str, Any], 
                                        performance_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate configuration using AI map strategy to guide procedural generation"""
        
        # Extract AI strategy parameters
        path_strategy = map_strategy.get('path_strategy', 'winding')
        path_length_target = map_strategy.get('path_length_target', 40)
        path_complexity = map_strategy.get('path_complexity', 0.5)
        buildable_space_target = map_strategy.get('buildable_space_target', 0.6)
        
        print(f"   🗺️ AI Strategy: {path_strategy} path, {path_length_target} length, {buildable_space_target:.0%} buildable")
        
        # Get base difficulty factors but modify them based on AI strategy
        factors = self.base_generator.derive_difficulty_factors(difficulty)
        
        # Override complexity with AI decision
        factors['complexity'] = path_complexity
        
        # Adjust buildable space based on AI strategy
        # Convert buildable space target to terrain density (inverse relationship)
        terrain_density = 1.0 - buildable_space_target
        factors['strategic_terrain_density'] = terrain_density
        factors['buildable_space'] = buildable_space_target
        
        # 🤖 NEW: Try AI-generated path first
        path = None
        if performance_data:
            ai_waypoints = self._generate_ai_path_waypoints(
                performance_data, width, height, path_strategy, path_length_target
            )
            if ai_waypoints:
                path = ai_waypoints
                print(f"   🤖✅ Using AI-generated path with {len(path)} waypoints")
        
        # Fallback to procedural generation if AI path fails
        if not path:
            print(f"   🔄 AI path failed, using procedural generation...")
            start = (1, height // 2)
            end = (width - 2, height // 2)
            min_path_length = max(20, min(70, int(path_length_target)))
            
            from .map_generation import generate_path_dfs
            path = generate_path_dfs(width, height, start, end, path_complexity, min_path_length)
        
        # Build terrain using AI strategy
        from .map_generation import build_terrain_grid_corrected
        
        # Convert AI buildable space target to difficulty for terrain generation
        # Higher buildable space = lower terrain difficulty
        terrain_difficulty = int((1.0 - buildable_space_target) * 100)
        terrain = build_terrain_grid_corrected(width, height, path, terrain_difficulty)
        
        # Now use standard procedural generation for wave config, but pass the AI-generated map
        base_config = self.base_generator.generate_config(
            difficulty=difficulty,
            width=width,
            height=height,
            total_waves=total_waves
        )
        
        # Override the map config with AI-generated map
        base_config['map_config']['default_map']['terrain'] = terrain
        base_config['map_config']['default_map']['path'] = path
        
        # Store AI map strategy in metadata
        base_config['_ai_map_strategy'] = map_strategy
        
        return base_config 
    
    def _apply_analytical_balancing(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Apply analytical balancing to ensure all waves are mathematically solvable"""
        try:
            # Extract tower catalog and game parameters
            towers = self._extract_tower_catalog(config)
            path_length, enemy_speed = self._extract_path_length_and_speed(config)
            
            # Get economic parameters
            game_config = config.get('game_config', {})
            start_cash = game_config.get('starting_money', 500)
            wave_config = config.get('wave_config', {})
            cash_per_wave = wave_config.get('money_config', {}).get('normal_wave_bonus', 50)
            
            total_waves = wave_config.get('total_waves', 80)
            problematic_waves = 0
            
            print(f"   Analyzing {total_waves} waves for balance...")
            
            # Balance each wave
            for wave_num in range(1, min(total_waves + 1, 21)):  # Analyze first 20 waves
                wave_enemies = self._extract_wave_enemies(config, wave_num)
                
                # Apply analytical balance
                balanced_enemies, hp_budget, damage_capacity, peak_dps = self._analytical_balance(
                    towers, wave_enemies, path_length, enemy_speed, wave_num, start_cash, cash_per_wave
                )
                
                # Check if balancing was needed
                original_hp = sum(e.hp for e in wave_enemies)
                balanced_hp = sum(e.hp for e in balanced_enemies)
                
                if balanced_hp < original_hp:
                    problematic_waves += 1
                    reduction = (1 - balanced_hp / original_hp) * 100
                    print(f"   Wave {wave_num}: Reduced HP by {reduction:.1f}% (Budget: {hp_budget:.0f})")
                
                # Update the config with balanced enemy data
                self._update_wave_config(config, wave_num, balanced_enemies, hp_budget)
            
            # Add balancing metadata
            config['_analytical_balancing'] = {
                'applied': True,
                'problematic_waves': problematic_waves,
                'waves_analyzed': min(total_waves, 20),
                'tower_catalog_size': len(towers),
                'path_length': path_length,
                'enemy_speed': enemy_speed
            }
            
            print(f"   Balanced {problematic_waves}/{min(total_waves, 20)} problematic waves")
            
        except Exception as e:
            print(f"   ⚠️ Analytical balancing failed: {e}")
            config['_analytical_balancing'] = {'applied': False, 'error': str(e)}
        
        return config
    
    def _analytical_balance(self, towers: List[TowerType], wave_enemies: List[WaveEnemy],
                           path_length: float, enemy_speed: float, wave: int,
                           start_cash: int = 500, cash_per_wave: int = 50,
                           safety_factor: float = 0.8, dt: float = 0.5):
        """Core analytical balancing algorithm"""
        # Prevent division by zero
        if enemy_speed <= 0:
            enemy_speed = 0.1  # Minimum speed to prevent division by zero
        path_travel_time = path_length / enemy_speed
        money_model = lambda t: start_cash + wave * cash_per_wave
        peak_dps = self._compute_peak_dps_curve(path_travel_time, towers, money_model, dt)
        damage_capacity = self._compute_damage_capacity(peak_dps, dt)
        hp_budget = math.floor(damage_capacity * safety_factor)
        balanced_enemies = self._cap_wave_hp(wave_enemies, hp_budget)
        return balanced_enemies, hp_budget, damage_capacity, peak_dps

    def _compute_peak_dps_curve(self, t_max: float, towers: List[TowerType], 
                               money_available_at_time: Callable[[float], float], dt: float = 0.5):
        """Compute the peak DPS curve over time"""
        times = [round(t, 2) for t in self._frange(0, t_max, dt)]
        peak_dps = {}
        spent = 0
        owned_towers = []  # (tower, built_time)
        
        for t in times:
            cash = money_available_at_time(t) - spent
            # Greedy: buy best DPS-per-cost tower if affordable
            best_choice, best_value = None, 0
            for tower in towers:
                if tower.cost <= cash:
                    remaining = t_max - t
                    dps_now = tower.dps_curve(0)
                    if remaining > 0:
                        dps_per_cost = dps_now * remaining / tower.cost
                        if dps_per_cost > best_value:
                            best_value, best_choice = dps_per_cost, tower
            
            if best_choice:
                spent += best_choice.cost
                owned_towers.append((best_choice, t))
            
            # Sum DPS of all owned towers at time t
            total_dps = sum(
                tower.dps_curve(t - built_time)
                for tower, built_time in owned_towers
                if t >= built_time + tower.build_time
            )
            peak_dps[t] = total_dps
        
        return peak_dps

    def _frange(self, start: float, stop: float, step: float):
        """Generate float range"""
        while start <= stop:
            yield start
            start += step

    def _compute_damage_capacity(self, peak_dps: Dict[float, float], dt: float) -> float:
        """Integrate peak DPS over time to get total damage capacity"""
        return sum(d * dt for d in peak_dps.values())

    def _cap_wave_hp(self, wave_enemies: List[WaveEnemy], hp_budget: float) -> List[WaveEnemy]:
        """Scale down enemy HP if total exceeds budget"""
        total_hp = sum(e.hp for e in wave_enemies)
        if total_hp <= hp_budget:
            return wave_enemies  # Already balanced
        
        # Scale down all HPs proportionally
        scale = hp_budget / total_hp
        balanced_enemies = []
        for e in wave_enemies:
            new_enemy = WaveEnemy(max(1, math.floor(e.hp * scale)))  # Minimum 1 HP
            balanced_enemies.append(new_enemy)
        
        return balanced_enemies

    def _extract_tower_catalog(self, config: Dict[str, Any]) -> List[TowerType]:
        """Extract tower catalog from config for analytical balancing"""
        # Define standard tower catalog based on game towers
        return [
                    TowerType('basic', cost=20, build_time=0, dps_curve=lambda t: 15),
        TowerType('sniper', cost=40, build_time=1, dps_curve=lambda t: 50 if t > 0 else 0),
        TowerType('cannon', cost=60, build_time=0.5, dps_curve=lambda t: 35),
        TowerType('laser', cost=80, build_time=0, dps_curve=lambda t: 25),
        TowerType('lightning', cost=50, build_time=0, dps_curve=lambda t: 30),
        TowerType('missile', cost=100, build_time=2, dps_curve=lambda t: 60 if t > 0 else 0),
        TowerType('explosive', cost=120, build_time=1, dps_curve=lambda t: 45),
        TowerType('flame', cost=70, build_time=0, dps_curve=lambda t: 20),
        TowerType('freezer', cost=90, build_time=0, dps_curve=lambda t: 15),
        TowerType('ice', cost=85, build_time=0, dps_curve=lambda t: 18),
        TowerType('poison', cost=75, build_time=0, dps_curve=lambda t: 22),
        TowerType('splash', cost=120, build_time=0, dps_curve=lambda t: 40),
        TowerType('antiair', cost=70, build_time=0, dps_curve=lambda t: 35),
        TowerType('detector', cost=25, build_time=0, dps_curve=lambda t: 5)  # Support tower
        ]

    def _extract_wave_enemies(self, config: Dict[str, Any], wave_num: int) -> List[WaveEnemy]:
        """Extract enemy HP data for a specific wave"""
        wave_config = config.get('wave_config', {})
        
        # Calculate base enemy count and HP
        spawn_config = wave_config.get('spawn_config', {})
        base_count = spawn_config.get('base_enemy_count', 15)
        
        # Apply wave progression for enemy count
        round_progression = wave_config.get('round_progression', {})
        enemy_increase = round_progression.get('enemy_increase_per_round', {})
        
        # Find appropriate wave range for enemy increase
        increase = 1  # default
        for wave_range, inc in enemy_increase.get('wave_ranges', {}).items():
            start_wave, end_wave = map(int, wave_range.split('-'))
            if start_wave <= wave_num <= end_wave:
                increase = inc
                break
        
        enemy_count = base_count + increase * (wave_num - 1)
        
        # Calculate base HP with scaling
        enemy_scaling = wave_config.get('enemy_scaling', {})
        health_per_wave = enemy_scaling.get('health_per_wave', 0.3)
        
        # Base HP estimation (simplified)
        base_hp = 8.0 + wave_num * 2.0
        health_multiplier = 1.0 + ((wave_num - 1) * health_per_wave)
        scaled_hp = base_hp * health_multiplier
        
        # Create enemy list
        return [WaveEnemy(scaled_hp) for _ in range(enemy_count)]

    def _extract_path_length_and_speed(self, config: Dict[str, Any]) -> tuple:
        """Extract path geometry and enemy speed from config"""
        # Calculate path length from map data
        map_config = config.get('map_config', {})
        if 'default_map' in map_config:
            path = map_config['default_map'].get('path', [])
            if len(path) > 1:
                # Calculate actual path length
                total_distance = 0.0
                for i in range(len(path) - 1):
                    dx = path[i+1][0] - path[i][0]
                    dy = path[i+1][1] - path[i][1]
                    total_distance += math.sqrt(dx*dx + dy*dy)
                path_length = total_distance
            else:
                path_length = 20.0  # Default
        else:
            path_length = 20.0  # Default
        
        # Enemy speed (average/slowest)
        enemy_speed = 1.0  # Default enemy speed (units per second)
        
        return path_length, enemy_speed

    def _update_wave_config(self, config: Dict[str, Any], wave_num: int, 
                           balanced_enemies: List[WaveEnemy], hp_budget: float):
        """Update the wave configuration with balanced enemy data"""
        # This is a simplified update - in a real implementation you would
        # need to map the balanced HP back to the actual wave configuration format
        
        if '_analytical_balancing' not in config:
            config['_analytical_balancing'] = {}
        
        if 'wave_adjustments' not in config['_analytical_balancing']:
            config['_analytical_balancing']['wave_adjustments'] = {}
        
        # Store the adjustment for this wave
        total_balanced_hp = sum(e.hp for e in balanced_enemies)
        config['_analytical_balancing']['wave_adjustments'][f'wave_{wave_num}'] = {
            'hp_budget': hp_budget,
            'balanced_total_hp': total_balanced_hp,
            'enemy_count': len(balanced_enemies),
            'avg_enemy_hp': total_balanced_hp / len(balanced_enemies) if balanced_enemies else 0
        }

    def _generate_ai_path_waypoints(self, performance_data: Dict[str, Any], width: int, height: int, 
                                   path_strategy: str, path_length_target: int) -> Optional[List[Tuple[int, int]]]:
        """Generate AI-designed path waypoints based on performance and strategy"""
        if not self.ai_available:
            return None
        
        score = performance_data.get('score', 50)
        win_flag = performance_data.get('win_flag', False)
        previous_difficulty = performance_data.get('previous_difficulty', 50)
        
        # Get multi-game context if available
        multi_game_context = performance_data.get('multi_game_context', {})
        avg_score = multi_game_context.get('avg_score', score) if multi_game_context else score
        trend = multi_game_context.get('trend', 'stable') if multi_game_context else 'stable'
        difficulty_progression = multi_game_context.get('difficulty_progression', []) if multi_game_context else []
        
        # Format difficulty progression for display
        difficulty_progression_str = " → ".join(map(str, difficulty_progression)) if difficulty_progression else "No progression data"
        
        prompt = f"""Design a strategic enemy path for tower defense based on player performance.

PLAYER PERFORMANCE ANALYSIS:
- Current Score: {score}/100
- Current Win: {win_flag}
- Multi-Game Average: {avg_score:.1f}/100
- Performance Trend: {trend}
- Previous Difficulty: {previous_difficulty}/100

DIFFICULTY PROGRESSION CONTEXT:
- Recent Difficulty Progression: {difficulty_progression_str}
- Pattern: {'Increasing challenge' if len(difficulty_progression) >= 3 and difficulty_progression[0] > difficulty_progression[-1] else 'Decreasing challenge' if len(difficulty_progression) >= 3 and difficulty_progression[0] < difficulty_progression[-1] else 'Stable challenge'} over {len(difficulty_progression)} games
- Experience Range: {min(difficulty_progression) if difficulty_progression else 'unknown'}-{max(difficulty_progression) if difficulty_progression else 'unknown'} difficulty

MAP DIMENSIONS: {width}x{height} (coordinates 0-{width-1}, 0-{height-1})
PATH STRATEGY: {path_strategy}
TARGET LENGTH: {path_length_target} segments

PATH DESIGN RULES:
- Start must be at LEFT edge (x=0 or x=1, y=middle area)
- End must be at RIGHT edge (x={width-2} or x={width-1}, y=middle area)
- Path must have {path_length_target} strategic waypoints
- Each waypoint must be valid coordinates within map bounds
- Path should reflect strategy: "direct" = straight, "winding" = curves, "maze" = complex turns

DIFFICULTY ADAPTATION:
- Low score ({score}% avg): Create longer, more forgiving paths with strategic placement opportunities
- High score: Create shorter, more challenging paths that test advanced placement skills
- Trend {trend}: {'Increase difficulty' if trend == 'improving' else 'Maintain challenge' if trend == 'stable' else 'Reduce difficulty'}

Generate waypoints that create strategic choke points and tower placement opportunities.
Consider where players would naturally want to place towers.

Output ONLY this JSON format (no other text):
{{
    "waypoints": [
        [start_x, start_y],
        [waypoint1_x, waypoint1_y],
        [waypoint2_x, waypoint2_y],
        ...,
        [end_x, end_y]
    ],
    "strategic_reasoning": "why this path design fits the player's skill level and performance"
}}

EXAMPLE for {width}x{height}:
{{
    "waypoints": [
        [1, {height//2}],
        [5, {height//2-2}],
        [10, {height//2+3}],
        [15, {height//2-1}],
        [{width-2}, {height//2}]
    ],
    "strategic_reasoning": "Path provides strategic placement opportunities while maintaining appropriate challenge level"
}}"""

        try:
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are a tower defense map designer. Generate strategic enemy paths. Output only JSON, no other text."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=600,
                temperature=0.8  # Higher temperature for more varied paths
            )
            
            ai_response = response.choices[0].message.content
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = ai_response[start_idx:end_idx]
                path_data = json.loads(json_str)
                
                waypoints = path_data.get('waypoints', [])
                reasoning = path_data.get('strategic_reasoning', 'AI-designed strategic path')
                
                # Validate waypoints
                if len(waypoints) >= 3:  # At least start, middle, end
                    # Ensure waypoints are within bounds
                    valid_waypoints = []
                    for x, y in waypoints:
                        x = max(0, min(width-1, int(x)))
                        y = max(0, min(height-1, int(y)))
                        valid_waypoints.append((x, y))
                    
                    print(f"   🤖🛤️ AI Path Design: {reasoning}")
                    return valid_waypoints
                else:
                    print("   ⚠️ AI generated insufficient waypoints, using fallback")
                    return None
                    
        except Exception as e:
            print(f"   ⚠️ AI path generation failed: {e}")
        
        return None

    def _analyze_enemy_reward_distribution(self, previous_config: Dict[str, Any], performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the previous level's enemy reward distribution to inform AI adjustments"""
        analysis = {
            'total_enemies_spawned': 0,
            'total_reward_potential': 0,
            'enemy_type_rewards': {},
            'reward_efficiency': {},
            'problematic_enemies': [],
            'wave_reward_balance': {},
            'scaling_effectiveness': {}
        }
        
        try:
            # Extract wave compositions from previous config
            wave_compositions = previous_config.get('wave_config', {}).get('wave_compositions', {})
            enemy_scaling = previous_config.get('wave_config', {}).get('enemy_scaling', {})
            money_config = previous_config.get('wave_config', {}).get('money_config', {})
            
            # Base enemy reward values (from enemy classes)
            base_rewards = {
                'BasicEnemy': 4, 'FastEnemy': 6, 'TankEnemy': 8, 'FlyingEnemy': 10,
                'ShieldedEnemy': 12, 'InvisibleEnemy': 15, 'ArmoredEnemy': 10,
                'RegeneratingEnemy': 12, 'TeleportingEnemy': 15, 'SplittingEnemy': 18,
                'FireElementalEnemy': 14, 'ToxicEnemy': 12, 'PhaseShiftEnemy': 20,
                'BlastProofEnemy': 16, 'SpectralEnemy': 25, 'CrystallineEnemy': 25,
                'ToxicMutantEnemy': 22, 'VoidEnemy': 30, 'AdaptiveEnemy': 35
            }
            
            # Analyze reward distribution across wave ranges
            for wave_range, enemies in wave_compositions.items():
                if not isinstance(enemies, list):
                    continue
                    
                wave_start = int(wave_range.split('-')[0])
                wave_end = int(wave_range.split('-')[1])
                wave_count = wave_end - wave_start + 1
                
                range_reward_total = 0
                range_enemy_count = 0
                range_spawn_rate = 0
                
                for enemy_data in enemies:
                    if len(enemy_data) >= 2:
                        enemy_type = enemy_data[0]
                        spawn_rate = enemy_data[1]
                        
                        # Calculate reward with scaling
                        base_reward = base_rewards.get(enemy_type, 5)
                        reward_scaling = enemy_scaling.get('reward_per_wave', 0.12)
                        avg_wave = (wave_start + wave_end) / 2
                        scaled_reward = base_reward * (1 + reward_scaling * avg_wave)
                        
                        # Track metrics
                        range_reward_total += scaled_reward * spawn_rate
                        range_enemy_count += spawn_rate
                        range_spawn_rate += spawn_rate
                        
                        # Individual enemy analysis
                        if enemy_type not in analysis['enemy_type_rewards']:
                            analysis['enemy_type_rewards'][enemy_type] = {
                                'base_reward': base_reward,
                                'scaled_reward': scaled_reward,
                                'spawn_rate': 0,
                                'total_reward_contribution': 0
                            }
                        
                        analysis['enemy_type_rewards'][enemy_type]['spawn_rate'] += spawn_rate
                        analysis['enemy_type_rewards'][enemy_type]['total_reward_contribution'] += scaled_reward * spawn_rate
                
                # Wave range analysis
                analysis['wave_reward_balance'][wave_range] = {
                    'total_reward_potential': range_reward_total,
                    'enemy_count': range_enemy_count,
                    'spawn_rate_total': range_spawn_rate,
                    'avg_reward_per_enemy': range_reward_total / max(range_enemy_count, 1),
                    'wave_count': wave_count
                }
            
            # Calculate reward efficiency (reward per difficulty)
            difficulty_ratings = {
                'BasicEnemy': 1, 'FastEnemy': 2, 'TankEnemy': 3, 'FlyingEnemy': 4,
                'ShieldedEnemy': 5, 'InvisibleEnemy': 6, 'ArmoredEnemy': 4,
                'RegeneratingEnemy': 5, 'TeleportingEnemy': 6, 'SplittingEnemy': 7,
                'FireElementalEnemy': 6, 'ToxicEnemy': 5, 'PhaseShiftEnemy': 7,
                'BlastProofEnemy': 6, 'SpectralEnemy': 8, 'CrystallineEnemy': 8,
                'ToxicMutantEnemy': 7, 'VoidEnemy': 8, 'AdaptiveEnemy': 9
            }
            
            for enemy_type, data in analysis['enemy_type_rewards'].items():
                difficulty = difficulty_ratings.get(enemy_type, 5)
                efficiency = data['scaled_reward'] / difficulty
                analysis['reward_efficiency'][enemy_type] = efficiency
                
                # Flag problematic enemies
                if efficiency > 8:  # Too rewarding for difficulty
                    analysis['problematic_enemies'].append({
                        'enemy': enemy_type,
                        'issue': 'over_rewarding',
                        'efficiency': efficiency,
                        'suggestion': 'reduce_spawn_rate_or_reward'
                    })
                elif efficiency < 2:  # Under-rewarding for difficulty
                    analysis['problematic_enemies'].append({
                        'enemy': enemy_type,
                        'issue': 'under_rewarding', 
                        'efficiency': efficiency,
                        'suggestion': 'increase_spawn_rate_or_reward'
                    })
            
            # Analyze scaling effectiveness
            reward_per_wave = enemy_scaling.get('reward_per_wave', 0.12)
            max_reward_multiplier = enemy_scaling.get('max_reward_multiplier', 20.0)
            
            analysis['scaling_effectiveness'] = {
                'reward_per_wave': reward_per_wave,
                'max_reward_multiplier': max_reward_multiplier,
                'scaling_rate': 'appropriate' if 0.08 <= reward_per_wave <= 0.15 else 'needs_adjustment'
            }
            
            # Calculate totals
            analysis['total_reward_potential'] = sum(
                data['total_reward_contribution'] for data in analysis['enemy_type_rewards'].values()
            )
            analysis['total_enemies_spawned'] = sum(
                data['spawn_rate'] for data in analysis['enemy_type_rewards'].values()
            )
            
        except Exception as e:
            print(f"Warning: Could not analyze enemy reward distribution: {e}")
            
        return analysis