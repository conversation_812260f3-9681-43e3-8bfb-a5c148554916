#!/usr/bin/env python3
"""
Full AI Configuration Generator

This system lets AI design complete tower defense maps and configurations
with minimal procedural generation fallbacks.
"""

import json
import random
import os
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime
import openai

class FullAIGenerator:
    """AI system that designs complete tower defense configurations"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.client = None
        self.ai_available = False
        
        # Try to initialize AI client
        if api_key:
            try:
                # Set up Azure OpenAI client with updated configuration
                self.client = openai.AzureOpenAI(
                    api_key=api_key,
                    api_version="2025-01-01-preview",
                    azure_endpoint="https://matrixnewopenai.openai.azure.com"
                )
                self.deployment_name = "gpt-4o"  # Azure deployment name
                self.ai_available = True
                print("🤖✅ FULL AI GENERATOR ACTIVE - AI will design complete maps!")
                print(f"   API Key: ...{api_key[-8:]}")
                print(f"   Endpoint: matrixnewopenai.openai.azure.com")
            except Exception as e:
                print(f"🤖❌ FULL AI initialization failed: {e}")
                print("🔄 Will use smart delegation fallback")
    
    def generate_complete_config_with_ai(self, performance_data: Dict, width: int = 20, 
                                       height: int = 15, total_waves: int = 80, previous_config: Dict = None) -> Dict[str, Any]:
        """Have AI generate the complete configuration including exact map layout"""
        
        if not self.ai_available:
            return self.smart_delegation_fallback(performance_data, width, height, total_waves)
        
        # Include previous config information if available
        previous_config_info = ""
        if previous_config:
            # Extract key information from the previous config
            prev_difficulty = previous_config.get('_generation_metadata', {}).get('difficulty', 'unknown')
            prev_terrain_info = self._analyze_config_terrain(previous_config)
            prev_wave_count = previous_config.get('wave_config', {}).get('total_waves', 'unknown')
            
            previous_config_info = f"""
PREVIOUS CONFIGURATION ANALYSIS:
- Previous Difficulty: {prev_difficulty}
- Previous Wave Count: {prev_wave_count}
- Previous Terrain Strategy: {prev_terrain_info}
- Previous Config Name: {previous_config.get('level_name', 'Unnamed')}

The AI should BUILD UPON this previous experience and adjust based on how the player performed.
"""
        
        # ★ Enhanced prompt with multi-game context support ★
        multi_game_context = performance_data.get('multi_game_context')
        if multi_game_context:
            # Use multi-game analysis for prompt
            game_summaries = "\n".join([
                f"Game {i+1}: Score {game['score']:.1f}%, {'Won' if game['win_flag'] else 'Lost'}, "
                f"Towers: {', '.join([f'{tower}({count})' for tower, count in game['towers_built'].items() if count > 0])}"
                for i, game in enumerate(multi_game_context['performance_summaries'])
            ])
            
            performance_analysis = f"""
MULTI-GAME PERFORMANCE ANALYSIS:
Games Analyzed: {multi_game_context['games_analyzed']}
Average Score: {multi_game_context['avg_score']:.1f}/100
Win Rate: {multi_game_context['win_rate']:.1f}%
Performance Trend: {multi_game_context['trend']}

RECENT GAME HISTORY:
{game_summaries}

AGGREGATED ANALYSIS:
Score: {performance_data.get('score', 50):.1f}/100 (multi-game average)
Victory Rate: {multi_game_context['win_rate']:.1f}% across {multi_game_context['games_analyzed']} games
Tower Diversity: {performance_data.get('tower_diversity', 3)} different types used across all games
Previous Config Difficulty: {performance_data.get('previous_difficulty', 50)}
"""
        else:
            # Single game analysis
            performance_analysis = f"""
SINGLE GAME PERFORMANCE DATA:
Score: {performance_data.get('score', 50):.1f}/100
Victory: {'Yes' if performance_data.get('win_flag', False) else 'No'}
Lives Remaining: {performance_data.get('lives_remaining', 10)}/{performance_data.get('starting_lives', 20)}
Tower Diversity: {performance_data.get('tower_diversity', 3)} different types used
Previous Config Difficulty: {performance_data.get('previous_difficulty', 50)}
"""
        
        # Create comprehensive prompt for full generation
        prompt = f"""You are an expert tower defense game designer. Generate a COMPLETE game configuration based on this player performance:

{performance_analysis}

{previous_config_info}

TERRAIN MECHANICS AND STRATEGIC USAGE:
- 0=Grass: Standard placement, all towers allowed, no bonuses/penalties
- 1=Path: Enemy route (normal speed), no towers can be placed here
- 2=Rock: Impassable obstacles, no towers, creates choke points
- 3=Water: ONLY Freezer/Splash towers allowed, +50% freeze effect duration
- 4=Forest: All towers allowed, -20% range but +30% damage (high-damage focus)
- 5=Sand: Enemy route (50% FASTER speed), use for difficult levels instead of normal path

CRITICAL TERRAIN PLACEMENT RULES:
- For EASY/MEDIUM levels: Use PATH (value 1) for normal enemy speed
- For DIFFICULT levels: Use SAND (value 5) for entire path to make all enemies 50% faster
- WATER/FOREST/GRASS: Place around the path for tower placement with different restrictions

MANDATORY STRATEGIC TERRAIN PLACEMENT RULES:
YOU MUST FOLLOW THESE EXACT PERCENTAGES BASED ON PLAYER WEAKNESSES:

If Tower Diversity = 0-2 types (FORCE EXPERIMENTATION):
→ Place EXACTLY 35-40% Water terrain (terrain value 3) AROUND the path for tower placement
→ Place EXACTLY 25-30% Forest terrain (terrain value 4) AROUND the path for tower placement
→ Place ONLY 20-25% Grass terrain (terrain value 0) AROUND the path
→ Use Rock obstacles (terrain value 2) for 10-15% to create chokepoints
→ Use normal PATH (1) for standard difficulty

If Lives Remaining < 50% of starting lives (NEEDS EASIER CHALLENGE):
→ Place 30-35% Water terrain (terrain value 3) AROUND the path for freeze tower placement
→ Use Rock placement (terrain value 2) to create defensive chokepoints
→ Use normal PATH (1) for standard enemy speed to help them learn

If Score >= 80% (HIGH SKILL - DIFFICULT CHALLENGE):
→ Use SAND (terrain value 5) for the ENTIRE enemy path to make all enemies 50% faster
→ Mix 20-25% each of Water and Forest terrain AROUND the path
→ Create a challenging level with fast enemies requiring advanced strategies

CRITICAL IMPLEMENTATION REQUIREMENTS:
1. ANALYZE the player's specific performance data above
2. IDENTIFY their primary weakness (diversity or speed management)
3. APPLY the corresponding terrain percentage rules EXACTLY
4. PLACE terrain strategically, not randomly - cluster similar terrain types
5. ENSURE the path travels through DIFFERENT terrain types to force adaptation
6. BUILD UPON the previous configuration experience if provided

TERRAIN GRID SPECIFICATIONS:
- Map size: {width}x{height} grid
- Use terrain values: 0(Grass), 1(Path-Normal), 2(Rock), 3(Water), 4(Forest), 5(Sand-Fast Path)
- Place Water/Forest/Grass/Rock AROUND the path for tower placement areas
- Choose path type based on difficulty: PATH (1) for normal levels, SAND (5) for difficult levels
- Ensure path connects start (top-left area) to finish (bottom-right area)
- Path design: Single path type throughout - either all normal PATH (1) OR all SAND (5)

ENEMY HEALTH AND SPEED MODIFICATIONS:
Based on the player's performance, you MUST adjust enemy base health and speed modifiers:

ENEMY HEALTH MODIFIER RULES:
- Score 0-30% AND Lost: enemy_buffs.max_health_modifier = 0.7 (reduce enemy health 30%)
- Score 31-50% AND Lost: enemy_buffs.max_health_modifier = 0.85 (reduce enemy health 15%)
- Score 51-70% AND Won: enemy_buffs.max_health_modifier = 1.0 (keep same health)
- Score 71-85% AND Won: enemy_buffs.max_health_modifier = 1.2 (increase enemy health 20%)
- Score 86-100% AND Won: enemy_buffs.max_health_modifier = 1.4 (increase enemy health 40%)

ENEMY SPEED MODIFIER RULES:
- Score 0-30% AND Lost: enemy_buffs.max_speed_modifier = 0.8 (reduce enemy speed 20%)
- Score 31-50% AND Lost: enemy_buffs.max_speed_modifier = 0.9 (reduce enemy speed 10%)
- Score 51-70% AND Won: enemy_buffs.max_speed_modifier = 1.0 (keep same speed)
- Score 71-85% AND Won: enemy_buffs.max_speed_modifier = 1.15 (increase enemy speed 15%)
- Score 86-100% AND Won: enemy_buffs.max_speed_modifier = 1.3 (increase enemy speed 30%)

MULTI-GAME PERFORMANCE ADJUSTMENTS:
If multi-game data shows consistent patterns, apply additional modifiers:
- Win Rate < 30%: Reduce both health and speed by additional 10%
- Win Rate > 80%: Increase both health and speed by additional 10%
- Performance Trend "improving": Increase difficulty by 5% on health/speed
- Performance Trend "declining": Decrease difficulty by 5% on health/speed

LEVEL NAME GENERATION:
Create a creative, fun level name that reflects the terrain strategy. Examples:
- Water focus: "Frozen Lakes Trial", "Glacial Challenge", "Ice Fields Test"
- Forest focus: "Ancient Woods", "Whispering Grove", "Forest Gauntlet"  
- Sand focus: "Desert Storm", "Sandstorm Siege", "Dune Runner"
- Mixed terrain: "Elemental Trial", "Terrain Master", "Adaptive Challenge"

YOU MUST OUTPUT COMPLETE VALID JSON WITH ALL REQUIRED SECTIONS. The JSON must include:

REQUIRED TOP-LEVEL SECTIONS (CRITICAL - DO NOT OMIT):
{{
  "level_name": "Creative Level Name",
  "game_config": {{
    "starting_lives": 20,
    "starting_money": 500,
    "lives_per_wave": 0
  }},
  "map_config": {{
    "default_map": {{
      "width": {width},
      "height": {height},
      "terrain": [[terrain grid with strategic placement]],
      "path": [[x,y] coordinates for enemy path]
    }}
  }},
  "wave_config": {{
    "total_waves": {total_waves},
    "spawn_config": {{"wave patterns with strategic enemies"}},
    "enemy_buffs": {{
      "max_health_modifier": [apply health modifier based on performance rules above],
      "max_speed_modifier": [apply speed modifier based on performance rules above]
    }}
  }},
  "tower_config": {{
    "base_costs": {{"basic": 20, "sniper": 40, "cannon": 60, etc}}
  }}
}}

OUTPUT ONLY THE COMPLETE JSON - NO OTHER TEXT. Ensure terrain placement follows the EXACT strategic percentages based on player performance analysis above.

EXAMPLE FOR HIGH SKILL PLAYER:
Player scored 85% → Use SAND (5) for entire enemy path to make all enemies 50% faster. Place 25% Water and 25% Forest terrain around the path for complex tower placement decisions.
"""

        try:
            print("🤖 AI designing complete configuration...")
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are an expert tower defense game designer. Generate complete, valid JSON configurations. Output only JSON, no explanatory text."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,  # High token limit for complete generation
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            
            # Try to parse JSON from response
            try:
                # Find JSON in response
                start_idx = ai_response.find('{')
                end_idx = ai_response.rfind('}') + 1
                if start_idx != -1 and end_idx > start_idx:
                    json_str = ai_response[start_idx:end_idx]
                    config = json.loads(json_str)
                    
                    # Validate that we got a complete config
                    if self.validate_ai_config(config, width, height):
                        print("🤖✅ AI GENERATED COMPLETE CONFIGURATION SUCCESSFULLY!")
                        print("🎨 This level was 100% designed by AI - including map layout, terrain strategy, and enemy waves!")
                        # Add metadata to track successful AI generation
                        config['_generation_metadata'] = config.get('_generation_metadata', {})
                        config['_generation_metadata']['generation_method'] = 'full_ai_complete'
                        config['_generation_metadata']['validation_passed'] = True
                        config['_generation_metadata']['ai_confidence'] = 'high'
                        config['_generation_metadata']['creation_type'] = '🤖 FULL AI GENERATION'
                        return config
                    else:
                        print("🤖⚠️ AI config validation failed, using smart delegation...")
                        print("📝 DEBUG: Full AI generation tried but failed validation")
                        return self.smart_delegation_fallback(performance_data, width, height, total_waves)
                else:
                    raise ValueError("No JSON found in AI response")
                    
            except (json.JSONDecodeError, ValueError) as e:
                print(f"⚠️ AI JSON parsing failed: {e}")
                print("🔄 Falling back to smart delegation...")
                return self.smart_delegation_fallback(performance_data, width, height, total_waves)
                
        except Exception as e:
            print(f"🤖❌ FULL AI GENERATION FAILED: {e}")
            print("📝 This means the AI couldn't create a complete level design")
            print("🔄 Using smart delegation fallback (AI instructions + procedural generation)...")
            return self.smart_delegation_fallback(performance_data, width, height, total_waves)

    def validate_ai_config(self, config: Dict[str, Any], width: int, height: int) -> bool:
        """Validate that AI generated a complete, valid configuration with strategic terrain placement"""
        try:
            # Check required top-level sections
            required_sections = ['game_config', 'map_config', 'wave_config', 'tower_config']
            for section in required_sections:
                if section not in config:
                    print(f"❌ Missing section: {section}")
                    return False
            
            # Check map config
            if 'default_map' not in config['map_config']:
                print("❌ Missing default_map in map_config")
                return False
                
            map_config = config['map_config']['default_map']
            if (map_config.get('width') != width or 
                map_config.get('height') != height):
                print("❌ Invalid map dimensions")
                return False
            
            if ('terrain' not in map_config or 
                len(map_config['terrain']) != height or
                len(map_config['terrain'][0]) != width):
                print("❌ Invalid terrain grid")
                return False
            
            # Check that path exists and is valid
            if 'path' not in map_config or len(map_config['path']) < 5:  # RELAXED: was 10
                print("❌ Invalid or missing path")
                return False
            
            # Check wave config
            wave_config = config['wave_config']
            if 'total_waves' not in wave_config or 'spawn_config' not in wave_config:
                print("❌ Invalid wave configuration")
                return False
            
            # NEW: Validate strategic terrain placement
            terrain_grid = map_config['terrain']
            if not self.validate_terrain_strategy(terrain_grid):
                print("❌ AI failed to follow strategic terrain placement rules")
                return False
            
            print("✅ AI config validation passed")
            return True
            
        except Exception as e:
            print(f"❌ Config validation error: {e}")
            return False

    def validate_terrain_strategy(self, terrain_grid: List[List[int]]) -> bool:
        """Validate that terrain placement follows strategic rules"""
        try:
            # Count terrain types
            terrain_counts = {}
            total_cells = 0
            
            for row in terrain_grid:
                for cell in row:
                    terrain_counts[cell] = terrain_counts.get(cell, 0) + 1
                    total_cells += 1
            
            # Calculate percentages
            terrain_percentages = {}
            for terrain_type, count in terrain_counts.items():
                terrain_percentages[terrain_type] = (count / total_cells) * 100
            
            # Check for strategic terrain usage
            water_pct = terrain_percentages.get(3, 0)    # Water terrain (around path)
            forest_pct = terrain_percentages.get(4, 0)   # Forest terrain (around path)
            sand_pct = terrain_percentages.get(5, 0)     # Sand path (fast enemy route)
            grass_pct = terrain_percentages.get(0, 0)    # Grass terrain (around path)
            path_pct = terrain_percentages.get(1, 0)     # Normal path (standard enemy route)
            
            # Validate that AI used strategic terrain placement
            if grass_pct > 90:  # RELAXED: was 85%
                print(f"⚠️ Warning: AI used too much Grass terrain ({grass_pct:.1f}%) - may be ignoring strategy")
                return False
            
            # Check for reasonable strategic terrain usage (Water/Forest for tower placement)
            strategic_tower_terrain = water_pct + forest_pct
            if strategic_tower_terrain < 8:  # RELAXED: was 15%
                print(f"⚠️ Warning: AI used very little strategic tower terrain ({strategic_tower_terrain:.1f}%) - ignoring strategy")
                return False
            
            # Validate path system - should be either normal path OR sand path, not both
            has_normal_path = path_pct > 0
            has_sand_path = sand_pct > 0
            
            if has_normal_path and has_sand_path:
                print(f"⚠️ Warning: AI mixed normal path ({path_pct:.1f}%) and sand path ({sand_pct:.1f}%) - should use one or the other")
                return False
            
            total_path = path_pct + sand_pct
            if total_path < 3:  # RELAXED: was 5%
                print(f"⚠️ Warning: Path system seems inadequate ({total_path:.1f}%) - need more path")
                return False
            
            if total_path > 35:  # RELAXED: was 25%
                print(f"⚠️ Warning: Too much path area ({total_path:.1f}%) - paths should be narrow")
                return False
            
            # Determine difficulty level based on path type
            difficulty_level = "HIGH (Sand Path)" if has_sand_path else "NORMAL (Standard Path)"
            
            print(f"✅ Strategic terrain usage:")
            print(f"   Tower Placement - Water: {water_pct:.1f}%, Forest: {forest_pct:.1f}%, Grass: {grass_pct:.1f}%")
            print(f"   Path System - {difficulty_level}: {total_path:.1f}%")
            return True
            
        except Exception as e:
            print(f"❌ Terrain strategy validation error: {e}")
            return False

    def smart_delegation_fallback(self, performance_data: Dict, width: int = 20, 
                                height: int = 15, total_waves: int = 80) -> Dict[str, Any]:
        """AI creates detailed instructions for procedural generators when full generation fails"""
        
        if not self.ai_available:
            print("🔄 No AI available, using basic procedural generation...")
            return self.basic_procedural_fallback(performance_data, width, height, total_waves)
        
        # Get AI instructions for map generation
        instruction_prompt = f"""Create DETAILED instructions for procedural map generation based on player performance:

PLAYER PERFORMANCE:
Score: {performance_data.get('score', 50):.1f}/100
Victory: {'Yes' if performance_data.get('win_flag', False) else 'No'}
Lives Remaining: {performance_data.get('lives_remaining', 10)}/{performance_data.get('starting_lives', 20)}
Tower Diversity: {performance_data.get('tower_diversity', 3)} types used

Provide detailed generation instructions in JSON format only:"""

        try:
            print("🤖 AI creating smart generation instructions...")
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are an expert game designer creating detailed procedural generation instructions. Output only JSON."},
                    {"role": "user", "content": instruction_prompt}
                ],
                max_tokens=1500,
                temperature=0.7
            )
            
            ai_response = response.choices[0].message.content
            
            # Parse AI instructions
            start_idx = ai_response.find('{')
            end_idx = ai_response.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                json_str = ai_response[start_idx:end_idx]
                instructions = json.loads(json_str)
                
                print("🤖➡️ AI GENERATED SMART INSTRUCTIONS (not full generation)")
                print("🔧 Using AI guidance with procedural execution...")
                return self.execute_ai_instructions(instructions, width, height, total_waves)
            else:
                raise ValueError("No JSON found in AI instructions")
                
        except Exception as e:
            print(f"⚠️ Smart delegation failed: {e}")
            print("🔄 Using basic procedural fallback...")
            return self.basic_procedural_fallback(performance_data, width, height, total_waves)

    def execute_ai_instructions(self, instructions: Dict[str, Any], width: int, 
                               height: int, total_waves: int) -> Dict[str, Any]:
        """Execute AI instructions using procedural algorithms"""
        
        print("🔧 Executing AI instructions with procedural algorithms...")
        
        # Import the base generator for procedural generation
        from .make_specific_config import ConfigGenerator
        base_generator = ConfigGenerator()
        
        # Extract AI parameters
        difficulty = instructions.get('difficulty_target', 50)
        
        # Round to integer for consistency
        difficulty = int(round(difficulty))
        
        # Generate base config using AI-determined difficulty
        base_config = base_generator.generate_config(
            difficulty=difficulty,
            width=width,
            height=height,
            total_waves=total_waves
        )
        
        # Add AI metadata
        base_config['_generation_metadata'].update({
            'generation_method': 'ai_smart_delegation',
            'ai_instructions': instructions,
            'ai_reasoning': instructions.get('reasoning', 'AI-guided procedural generation'),
            'creation_type': '🤖➡️🔧 AI INSTRUCTIONS + PROCEDURAL EXECUTION'
        })
        
        print("🔧✅ AI instructions executed with procedural generation")
        return base_config

    def basic_procedural_fallback(self, performance_data: Dict, width: int = 20, 
                                height: int = 15, total_waves: int = 80) -> Dict[str, Any]:
        """Basic procedural generation when AI is completely unavailable"""
        
        print("🔧❌ USING BASIC PROCEDURAL GENERATION (AI completely unavailable)")
        
        # Import and use the base generator
        from .make_specific_config import ConfigGenerator
        base_generator = ConfigGenerator()
        
        # Determine difficulty from performance
        # FIXED: Prioritize win/loss status over score percentage
        score = performance_data.get('score', 50)
        win_flag = performance_data.get('win_flag', False)
        previous_difficulty = performance_data.get('previous_difficulty', 50)
        
        if win_flag:
            # Player WON - can increase difficulty based on performance
            if score >= 80:
                difficulty_change = 10  # Moderate increase for excellent performance
            elif score >= 60:
                difficulty_change = 5   # Small increase for good performance
            elif score >= 40:
                difficulty_change = 2   # Minimal increase for average performance
            else:
                difficulty_change = 0   # No change for poor but winning performance
        else:
            # Player LOST - always reduce difficulty to help them succeed
            if score >= 70:
                difficulty_change = -5  # Small decrease for high-scoring losses
            elif score >= 50:
                difficulty_change = -10 # Moderate decrease for medium-scoring losses  
            elif score >= 30:
                difficulty_change = -15 # Large decrease for low-scoring losses
            else:
                difficulty_change = -20 # Major decrease for very poor performance
        
        # Apply difficulty change with bounds checking
        difficulty = max(10, min(90, previous_difficulty + difficulty_change))
        
        # Round to integer for consistency
        difficulty = int(round(difficulty))
        
        # Generate with procedural system
        config = base_generator.generate_config(
            difficulty=difficulty,
            width=width,
            height=height,
            total_waves=total_waves
        )
        
        # Add metadata
        config['_generation_metadata'].update({
            'generation_method': 'procedural_fallback',
            'original_ai_target': True,
            'performance_based_difficulty': difficulty,
            'creation_type': '🔧❌ PROCEDURAL FALLBACK (AI failed)'
        })
        
        print(f"🔧✅ Procedural fallback completed with difficulty {difficulty}")
        print("⚠️ This level was created by algorithms, not AI")
        return config

    def generate_from_performance_file(self, performance_file: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """Generate complete config from a performance data file"""
        
        # Load performance data
        from .adaptive_config_generator import load_performance_from_file
        performance = load_performance_from_file(performance_file)
        
        # Convert to performance data dict
        performance_data = {
            'score': performance.score,
            'win_flag': performance.win_flag,
            'lives_remaining': performance.lives_remaining,
            'starting_lives': performance.starting_lives,
            'towers_built': performance.towers_built,
            'tower_diversity': len([t for t, count in performance.towers_built.items() if count > 0]),
            'previous_difficulty': performance.previous_config.get('_generation_metadata', {}).get('difficulty', 50)
        }
        
        # Extract previous configuration from performance data
        previous_config = performance.previous_config if hasattr(performance, 'previous_config') and performance.previous_config else None
        
        print(f"🔍 Using previous config data: {'Yes' if previous_config else 'No'}")
        if previous_config:
            prev_name = previous_config.get('level_name', 'Unnamed')
            prev_difficulty = previous_config.get('_generation_metadata', {}).get('difficulty', 'unknown')
            print(f"   Previous Level: {prev_name} (Difficulty: {prev_difficulty})")
        
        # Generate complete config with previous config context
        config = self.generate_complete_config_with_ai(performance_data, previous_config=previous_config)
        
        # Save if output path provided
        if output_path:
            with open(output_path, 'w') as f:
                json.dump(config, f, indent=2)
            print(f"✅ Full AI configuration saved to {output_path}")
        
        return config

    def _analyze_config_terrain(self, config: Dict) -> str:
        """Analyze terrain strategy from a previous configuration"""
        try:
            if 'map_config' not in config or 'default_map' not in config['map_config']:
                return "Unknown terrain layout"
            
            terrain_grid = config['map_config']['default_map'].get('terrain', [])
            if not terrain_grid:
                return "No terrain data available"
            
            # Count terrain types
            terrain_counts = {}
            total_cells = 0
            
            for row in terrain_grid:
                for cell in row:
                    terrain_counts[cell] = terrain_counts.get(cell, 0) + 1
                    total_cells += 1
            
            if total_cells == 0:
                return "Empty terrain grid"
            
            # Calculate percentages
            terrain_map = {
                0: "Grass",
                1: "Normal Path", 
                2: "Rock",
                3: "Water",
                4: "Forest",
                5: "Sand Path"
            }
            
            terrain_summary = []
            for terrain_type, count in terrain_counts.items():
                percentage = (count / total_cells) * 100
                terrain_name = terrain_map.get(terrain_type, f"Unknown({terrain_type})")
                if percentage >= 5:  # Only include significant terrain types
                    terrain_summary.append(f"{terrain_name}: {percentage:.1f}%")
            
            return ", ".join(terrain_summary) if terrain_summary else "Mostly Grass terrain"
            
        except Exception as e:
            return f"Error analyzing terrain: {e}"


def main():
    """Interactive full AI generator"""
    
    print("=== Full AI Configuration Generator ===")
    print("This system lets AI design complete maps and configurations!\n")
    
    # Initialize with API key
    api_key = os.getenv('AZURE_OPENAI_API_KEY') or os.getenv('OPENAI_API_KEY')
    if not api_key:
        api_key = "dcd901140ba54ea6aa738ec0e069b9f4"  # Fallback
    
    generator = FullAIGenerator(api_key)
    
    while True:
        print("\n" + "="*50)
        print("Choose generation method:")
        print("1. From performance file - AI analyzes and creates complete config")
        print("2. Manual performance input - Enter stats and let AI design")
        print("3. Quick test - Generate config for average player")
        print("4. Quit")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == '4' or choice.lower() in ['quit', 'q', 'exit']:
            print("Goodbye!")
            break
        
        try:
            if choice == '1':
                # From performance file
                from .adaptive_config_generator import list_performance_files
                files = list_performance_files()
                
                if not files:
                    print("❌ No performance files found. Play some games first!")
                    continue
                
                print(f"\nAvailable performance files:")
                for i, filename in enumerate(files, 1):
                    print(f"   {i}. {filename}")
                
                file_choice = input(f"\nSelect file (1-{len(files)}): ").strip()
                file_index = int(file_choice) - 1
                
                if 0 <= file_index < len(files):
                    selected_file = files[file_index]
                    
                    # Generate output path
                    config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config')
                    os.makedirs(config_dir, exist_ok=True)
                    
                    counter = 1
                    while True:
                        output_name = f"ai_full_{counter}.json"
                        output_path = os.path.join(config_dir, output_name)
                        if not os.path.exists(output_path):
                            break
                        counter += 1
                    
                    print(f"\n🤖 Generating complete AI configuration from {selected_file}...")
                    config = generator.generate_from_performance_file(selected_file, output_path)
                    
                    print(f"\n✅ Full AI configuration generated!")
                    print(f"   Method: {config['_generation_metadata']['generation_method']}")
                    print(f"   Saved as: {output_name}")
                    
                    if '_generation_metadata' in config and 'ai_reasoning' in config['_generation_metadata']:
                        print(f"   AI Reasoning: {config['_generation_metadata']['ai_reasoning']}")
                
            elif choice == '2':
                # Manual input
                print("\nEnter player performance data:")
                score = float(input("Performance score (0-100): ") or "50")
                won = input("Did player win? (y/n): ").strip().lower() == 'y'
                lives = int(input("Lives remaining: ") or "10")
                starting_lives = int(input("Starting lives: ") or "20")
                diversity = int(input("Tower types used: ") or "3")
                
                performance_data = {
                    'score': score,
                    'win_flag': won,
                    'lives_remaining': lives,
                    'starting_lives': starting_lives,
                    'towers_built': {},
                    'tower_diversity': diversity,
                    'previous_difficulty': 50
                }
                
                config = generator.generate_complete_config_with_ai(performance_data)
                
                # Save config
                config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config')
                os.makedirs(config_dir, exist_ok=True)
                
                counter = 1
                while True:
                    output_name = f"ai_manual_{counter}.json"
                    output_path = os.path.join(config_dir, output_name)
                    if not os.path.exists(output_path):
                        break
                    counter += 1
                
                with open(output_path, 'w') as f:
                    json.dump(config, f, indent=2)
                
                print(f"\n✅ AI configuration generated and saved as {output_name}!")
                
            elif choice == '3':
                # Quick test
                print("\n🤖 Generating test configuration for average player...")
                
                test_performance = {
                    'score': 55.0,
                    'win_flag': False,
                    'lives_remaining': 8,
                    'starting_lives': 20,
                    'towers_built': {},
                    'tower_diversity': 4,
                    'previous_difficulty': 50
                }
                
                config = generator.generate_complete_config_with_ai(test_performance)
                
                config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config')
                os.makedirs(config_dir, exist_ok=True)
                
                counter = 1
                while True:
                    output_name = f"ai_test_{counter}.json"
                    output_path = os.path.join(config_dir, output_name)
                    if not os.path.exists(output_path):
                        break
                    counter += 1
                
                with open(output_path, 'w') as f:
                    json.dump(config, f, indent=2)
                
                print(f"✅ Test configuration saved as {output_name}!")
                
        except ValueError:
            print("❌ Invalid input. Please enter valid numbers.")
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    main() 